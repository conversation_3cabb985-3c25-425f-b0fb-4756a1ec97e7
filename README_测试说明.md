# AI写作功能压力测试脚本

## 📋 功能说明

这个脚本专门用于测试你的AI写作功能在高并发情况下的表现，特别是：
- ✅ **并发请求处理能力** - 同时发送20个AI请求
- ✅ **计费系统准确性** - 验证每个请求是否正确扣费
- ✅ **中断处理机制** - 模拟用户中断请求的情况
- ✅ **系统稳定性** - 检查是否有内存泄漏或崩溃

## 🎯 测试场景

脚本会模拟你描述的使用场景：
1. **快速连续发送20个"生成3000字小说"请求**
2. **随机中断30%的请求**（模拟用户点击停止）
3. **监控每个请求的计费情况**
4. **统计成功率和响应时间**

## 🚀 使用方法

### 方法1: 直接运行（推荐）
```bash
# Windows
双击 run_test.bat

# 或者命令行
node ai_stress_test.js
```

### 方法2: 自定义配置
编辑 `ai_stress_test.js` 文件中的 `CONFIG` 部分：

```javascript
const CONFIG = {
  CONCURRENT_REQUESTS: 20,        // 并发请求数量
  REQUEST_INTERVAL: 100,          // 请求间隔(毫秒)
  INTERRUPT_DELAY: 2000,          // 中断延迟(毫秒)
  INTERRUPT_PROBABILITY: 0.3,     // 中断概率(30%)
  // ... 其他配置
};
```

## 📊 测试报告示例

```
📈 压力测试报告
============================================================
🕒 总耗时: 45230ms (45.23秒)
📊 请求统计:
   - 总请求数: 20
   - 成功请求: 14 (70.0%)
   - 失败请求: 0 (0.0%)
   - 中断请求: 6 (30.0%)
⏱️  响应时间:
   - 平均响应时间: 8524ms
   - 最快响应: 3200ms
   - 最慢响应: 15600ms
💰 计费统计:
   - 总消耗Token: 45,230
   - 总费用: 2,456,780字符
   - 平均每请求费用: 175,484字符
🎯 测试结论:
✅ 系统表现良好，能够处理高并发请求
💳 计费系统正常工作，能够正确统计费用
```

## 🔧 技术细节

### 认证信息
脚本使用从浏览器捕获的真实认证信息：
- **JWT Token**: 自动从你的登录会话获取
- **用户UUID**: `e432fabd-c944-4c01-9dcd-2e687287c8dc`
- **提示词ID**: `056c4636-14d7-4eb1-abd0-db58ad305728`

### 请求格式
完全模拟真实的前端请求：
```javascript
{
  "messages": [
    {
      "role": "system",
      "content": "__USER_PROMPT_ID__:056c4636-14d7-4eb1-abd0-db58ad305728"
    },
    {
      "role": "user", 
      "content": "生成3000字小说"
    }
  ],
  "model": "kelaode",
  "temperature": 0.7,
  "max_tokens": 64000
}
```

### 中断模拟
- 30%的请求会在2秒后被强制中断
- 模拟用户点击停止按钮的行为
- 测试RequestManager的异常处理能力

## 🎯 测试目标

1. **验证并发处理** - 20个同时请求不会导致系统崩溃
2. **检查计费准确性** - 每个请求（包括中断的）都应该正确计费
3. **测试ID管理** - 每个请求都有唯一ID，不会冲突
4. **评估响应性能** - 平均响应时间和成功率
5. **发现潜在问题** - 内存泄漏、死锁、重复计费等

## ⚠️ 注意事项

1. **确保服务器运行** - 测试前确保 `npm run dev` 正在运行
2. **检查余额** - 测试会消耗大量字符，确保账户余额充足
3. **监控日志** - 同时观察终端日志，查看计费情况
4. **Token有效期** - JWT Token有效期约1小时，过期需要重新获取

## 🔄 重新获取认证信息

如果Token过期，需要：
1. 重新登录网站
2. 发送一次AI请求
3. 从浏览器开发者工具获取新的Authorization头
4. 更新脚本中的 `JWT_TOKEN`

## 📞 问题排查

- **连接被拒绝**: 检查服务器是否在localhost:3000运行
- **401认证错误**: JWT Token可能过期，需要重新获取
- **403权限错误**: 检查用户权限和余额
- **超时错误**: 可能是服务器负载过高或网络问题
