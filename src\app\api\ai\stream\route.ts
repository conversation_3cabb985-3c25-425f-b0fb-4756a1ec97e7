/**
 * AI流式API中间层路由
 * 接收前端请求，转发到目标AI服务器，返回流式响应
 */
import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';
import { createClient } from '@supabase/supabase-js';
import { BackendBillingService } from '@/lib/billing/backend';

// 多API配置支持不同模型使用不同密钥
const API_CONFIGS = {
  'ceshi': { // 测试版模型
    baseURL: "http://114.55.8.214:10000/v1",
    apiKey: "sk-EbCjOT3xJu6uavX57lPSwacenSnmUeF540FCdc95pg7pF6ac"
  },
  'kelaode': { // 克劳德模型
    baseURL: "http://114.55.8.214:10000/v1",
    apiKey: "sk-EbCjOT3xJu6uavX57lPSwacenSnmUeF540FCdc95pg7pF6ac"
  }
};

// 硬编码内部API密钥 - 用于调用计费API
const INTERNAL_API_KEY = "billing_internal_2024_secure_key_xyz789";

// 创建Supabase客户端用于获取用户信息
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 用户会话信息（内存存储）
interface UserSession {
  userUuid: string;           // 用户UUID（主键）
  accessToken: string;        // 访问令牌
  email?: string;             // 用户邮箱
  lastActiveAt: number;       // 最后活跃时间
  activeRequests: Set<string>; // 活跃请求ID集合
}

// AI请求信息
interface AIRequest {
  requestId: string;          // 请求ID
  userUuid: string;           // 用户UUID
  model: string;              // 模型代号
  status: 'active' | 'billing' | 'completed';
  createdAt: number;          // 创建时间
  finalUsage?: any;           // 最终Usage
  billed: boolean;            // 是否已扣费
  usedPromptId?: string;      // 使用的提示词ID
  usesFreeQuota: boolean;     // 是否使用免费额度
}

// 精简版会话管理器
class SimpleSessionManager {
  // 核心存储（内存）
  private sessions = new Map<string, UserSession>();        // userUuid -> UserSession
  private requests = new Map<string, AIRequest>();          // requestId -> AIRequest
  private billingQueue = new Set<string>();                 // 待扣费请求队列

  // 配置
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000;       // 30分钟
  private readonly REQUEST_TIMEOUT = 10 * 60 * 1000;       // 10分钟
  private readonly MAX_REQUESTS_PER_USER = 10;             // 每用户最多10个并发请求

  /**
   * 验证并获取用户会话
   */
  async validateAndGetSession(authHeader: string): Promise<UserSession | null> {
    if (!authHeader?.startsWith('Bearer ')) return null;

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) return null;

    // 获取或创建会话
    let session = this.sessions.get(user.id);
    if (!session) {
      session = {
        userUuid: user.id,
        accessToken: token,
        email: user.email,
        lastActiveAt: Date.now(),
        activeRequests: new Set()
      };
      this.sessions.set(user.id, session);
    } else {
      // 更新活跃时间和token
      session.lastActiveAt = Date.now();
      session.accessToken = token;
    }

    return session;
  }

  /**
   * 创建新请求
   */
  createRequest(userUuid: string, model: string, usedPromptId?: string, usesFreeQuota: boolean = false): string | null {
    const session = this.sessions.get(userUuid);
    if (!session) return null;

    // 检查并发限制
    if (session.activeRequests.size >= this.MAX_REQUESTS_PER_USER) {
      return null;
    }

    // 生成请求ID
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    // 创建请求记录
    const request: AIRequest = {
      requestId,
      userUuid,
      model,
      status: 'active',
      createdAt: Date.now(),
      billed: false,
      usedPromptId,
      usesFreeQuota
    };

    // 存储请求
    this.requests.set(requestId, request);
    session.activeRequests.add(requestId);

    console.log(`[SessionManager] 创建请求: ${requestId}, 用户: ${userUuid}, 模型: ${model}`);
    return requestId;
  }

  /**
   * 更新请求Usage
   */
  updateRequestUsage(requestId: string, usage: any): boolean {
    const request = this.requests.get(requestId);
    if (!request || request.status !== 'active') return false;

    request.finalUsage = usage;
    console.log(`[SessionManager] 更新Usage: ${requestId}, tokens: ${usage.total_tokens}`);
    return true;
  }

  /**
   * 完成请求并触发扣费
   */
  async completeRequest(requestId: string): Promise<void> {
    const request = this.requests.get(requestId);
    if (!request || request.billed) return;

    // 标记为计费状态
    request.status = 'billing';

    // 添加到扣费队列
    this.billingQueue.add(requestId);

    // 异步处理扣费
    this.processBilling(requestId);

    console.log(`[SessionManager] 完成请求: ${requestId}`);
  }

  /**
   * 处理扣费（异步）
   */
  private async processBilling(requestId: string): Promise<void> {
    const request = this.requests.get(requestId);
    if (!request || request.billed || !request.finalUsage) return;

    try {
      // 计算费用
      const totalCost = this.calculateTokenCost(
        request.finalUsage.prompt_tokens,
        request.finalUsage.total_tokens,
        request.model
      );

      // 调用扣费API
      const billingResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/billing/deduct`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${INTERNAL_API_KEY}`
        },
        body: JSON.stringify({
          userUuid: request.userUuid,
          amount: totalCost,
          model: request.model,
          promptId: request.usedPromptId,
          usesFreeQuota: request.usesFreeQuota
        })
      });

      const result = await billingResponse.json();

      if (result.success) {
        request.billed = true;
        request.status = 'completed';
        console.log(`[SessionManager] 扣费成功: ${requestId}, 金额: ${totalCost}`);
      } else {
        console.error(`[SessionManager] 扣费失败: ${requestId}, 原因: ${result.message}`);
      }

    } catch (error) {
      console.error(`[SessionManager] 扣费异常: ${requestId}`, error);
    } finally {
      // 从扣费队列移除
      this.billingQueue.delete(requestId);

      // 从用户活跃请求中移除
      const session = this.sessions.get(request.userUuid);
      if (session) {
        session.activeRequests.delete(requestId);
      }

      // 延迟删除请求记录（保留5分钟用于调试）
      setTimeout(() => {
        this.requests.delete(requestId);
      }, 5 * 60 * 1000);
    }
  }

  /**
   * 计算Token费用
   */
  private calculateTokenCost(promptTokens: number, totalTokens: number, modelCode: string): number {
    const outputTokens = totalTokens - promptTokens;
    const rates = this.MODEL_BILLING_RATES[modelCode as keyof typeof this.MODEL_BILLING_RATES] || this.MODEL_BILLING_RATES['ceshi'];
    return promptTokens * rates.inputRate + outputTokens * rates.outputRate;
  }

  private readonly MODEL_BILLING_RATES = {
    'ceshi': { inputRate: 5, outputRate: 40 },
    'kelaode': { inputRate: 10, outputRate: 55 }
  };

  /**
   * 获取用户活跃请求数
   */
  getUserActiveRequestCount(userUuid: string): number {
    const session = this.sessions.get(userUuid);
    return session ? session.activeRequests.size : 0;
  }

  /**
   * 清理过期数据
   */
  cleanup(): void {
    const now = Date.now();

    // 清理过期会话
    for (const [userUuid, session] of Array.from(this.sessions.entries())) {
      if (now - session.lastActiveAt > this.SESSION_TIMEOUT) {
        // 强制完成该用户的所有活跃请求
        for (const requestId of Array.from(session.activeRequests)) {
          this.completeRequest(requestId);
        }
        this.sessions.delete(userUuid);
        console.log(`[SessionManager] 清理过期会话: ${userUuid}`);
      }
    }

    // 清理超时请求
    for (const [requestId, request] of Array.from(this.requests.entries())) {
      if (request.status === 'active' && now - request.createdAt > this.REQUEST_TIMEOUT) {
        this.completeRequest(requestId);
        console.log(`[SessionManager] 清理超时请求: ${requestId}`);
      }
    }
  }

  /**
   * 获取系统状态
   */
  getStats() {
    return {
      totalSessions: this.sessions.size,
      totalRequests: this.requests.size,
      activeRequests: Array.from(this.requests.values()).filter(r => r.status === 'active').length,
      billingQueue: this.billingQueue.size,
      memoryUsage: {
        sessions: this.sessions.size,
        requests: this.requests.size
      }
    };
  }
}

// 全局会话管理器实例
const sessionManager = new SimpleSessionManager();

// 定期清理过期数据
setInterval(() => {
  sessionManager.cleanup();
}, 5 * 60 * 1000); // 每5分钟清理一次



// 消息类型
interface Message {
  role: 'user' | 'system' | 'assistant';
  content: string;
}

// 请求体类型
interface StreamRequest {
  messages: Message[];
  model: string;
  temperature?: number;
  max_tokens?: number;
}

// 模型代号到真实模型名称的转换映射表
const MODEL_CODE_TO_NAME: Record<string, string> = {
  'ceshi': 'kimi-k2', // 测试版
  'kelaode': 'deepseek-r1', // 克劳德
};

// 转换模型代号为真实模型名称
const convertModelCodeToName = (modelCode: string): string => {
  const realModelName = MODEL_CODE_TO_NAME[modelCode];
  if (!realModelName) {
    console.warn(`未知的模型代号: ${modelCode}, 使用默认模型`);
    return 'gemini-2.5-pro'; // 默认使用测试版模型
  }
  console.log(`模型代号转换: ${modelCode} -> ${realModelName}`);
  return realModelName;
};

/**
 * 获取用户提示词内容
 */
const getUserPromptContent = async (promptId: string, request: NextRequest): Promise<string | null> => {
  try {
    console.log(`[Stream] 请求用户提示词ID: ${promptId}`);

    // 从原始请求中获取认证信息
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // 传递Authorization头
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    // 传递Cookie头
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      headers['Cookie'] = cookieHeader;
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/prompt?id=${promptId}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      console.error(`[Stream] 获取提示词失败: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    if (data.success && data.data?.content) {
      console.log(`[Stream] 成功获取用户提示词: ${data.data.title}`);
      return data.data.content;
    }

    console.error('[Stream] 提示词响应格式错误:', data);
    return null;
  } catch (error) {
    console.error('[Stream] 获取用户提示词异常:', error);
    return null;
  }
};

/**
 * 处理消息中的用户提示词ID
 * 返回处理后的消息和使用的提示词ID
 */
const processUserPrompts = async (messages: Message[], request: NextRequest): Promise<{
  processedMessages: Message[];
  usedPromptId: string | null;
}> => {
  const processedMessages: Message[] = [];
  let usedPromptId: string | null = null;

  for (const message of messages) {
    // 检查是否是系统消息
    if (message.role === 'system') {
      // 检查是否为用户提示词ID格式
      if (message.content.match(/^__USER_PROMPT_ID__:[a-zA-Z0-9-]+$/)) {
      try {
        // 提取用户提示词ID
        const promptIdMatch = message.content.match(/__USER_PROMPT_ID__:([a-zA-Z0-9-]+)/);
        if (!promptIdMatch) {
          console.error('[Stream] 无法提取用户提示词ID');
          processedMessages.push(message);
          continue;
        }

        const promptId = promptIdMatch[1];
        console.log(`[Stream] 检测到用户提示词ID: ${promptId}`);

        // 记录使用的提示词ID
        usedPromptId = promptId;

        // 获取用户提示词内容（等待最多5秒）
        const timeoutPromise = new Promise<string | null>((resolve) => {
          setTimeout(() => resolve(null), 5000);
        });

        const contentPromise = getUserPromptContent(promptId, request);
        const promptContent = await Promise.race([contentPromise, timeoutPromise]);

        if (promptContent) {
          // 直接使用用户提示词内容，不添加任何前置包装
          processedMessages.push({
            role: 'system',
            content: promptContent
          });

          console.log('[Stream] 用户提示词处理成功，使用纯净内容');
        } else {
          console.error('[Stream] 获取用户提示词超时或失败，使用原始消息');
          processedMessages.push(message);
          // 如果获取失败，清除提示词ID
          usedPromptId = null;
        }
      } catch (error) {
        console.error('[Stream] 处理用户提示词失败:', error);
        processedMessages.push(message);
        // 如果处理失败，清除提示词ID
        usedPromptId = null;
      }
      } else {
        // 自定义提示词：直接使用原始内容，不添加任何前置包装
        console.log('[Stream] 检测到自定义提示词，使用纯净内容');

        processedMessages.push({
          role: 'system',
          content: message.content
        });

        console.log('[Stream] 自定义提示词处理成功');
      }
    } else {
      // 非系统消息，直接添加
      processedMessages.push(message);
    }
  }

  return {
    processedMessages,
    usedPromptId
  };
};

/**
 * 错误处理函数
 */
const handleAIError = (error: any): string => {
  console.error('AI服务错误:', error);
  const errorMessage = error?.message || JSON.stringify(error) || '未知错误';

  if (errorMessage.includes('API key not configured')) {
    return '401';
  }

  if (error instanceof OpenAI.APIError) {
    if (error.status === 401) {
      return '401';
    }
    if (error.status === 429) {
      return '429';
    }
    if (error.status === 403) {
      return '403';
    }
    if (error.status === 500) {
      return '500';
    }
    if (error.status === 503) {
      return '503';
    }
    if (error.code === 'invalid_api_key') {
      return '401';
    }
    // 返回具体的状态码，如果没有状态码则返回500
    return error.status ? error.status.toString() : '500';
  }

  if (errorMessage.includes('token') || errorMessage.includes('context_length_exceeded')) {
    return '413';
  }
  if (errorMessage.includes('network') || errorMessage.includes('timeout') || errorMessage.includes('fetch failed')) {
    return '503';
  }
  if (errorMessage.includes('authentication') || errorMessage.includes('认证')) {
    return '401';
  }

  return '500';
};

/**
 * 获取用户UUID和访问令牌
 */
const getUserInfo = async (request: NextRequest): Promise<{ userUuid: string; accessToken: string } | null> => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) return null;

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) return null;
    return {
      userUuid: user.id,
      accessToken: token
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

/**
 * 调用内部计费API进行扣费
 */
const callBillingAPI = async (
  userUuid: string,
  amount: number,
  modelCode?: string,
  promptId?: string
): Promise<{ success: boolean; message?: string }> => {
  try {
    // 构建完整的URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const billingUrl = `${baseUrl}/api/billing/deduct`;

    console.log(`[Stream] 调用计费API: ${billingUrl}`);
    console.log(`[Stream] 计费参数: 用户=${userUuid}, 金额=${amount}, 模型=${modelCode || '未指定'}, 提示词=${promptId || '无'}`);

    const response = await fetch(billingUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        internalApiKey: INTERNAL_API_KEY,
        userUuid,
        amount,
        modelCode,
        promptId
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[Stream] 计费API调用失败: ${response.status} ${response.statusText}`);
      console.error(`[Stream] 错误详情: ${errorText}`);
      return {
        success: false,
        message: `计费API调用失败: ${response.status} - ${errorText}`
      };
    }

    const result = await response.json();
    console.log(`[Stream] 计费API响应:`, result);
    return result;
  } catch (error) {
    console.error('[Stream] 调用计费API异常:', error);
    return {
      success: false,
      message: '调用计费API异常'
    };
  }
};

// 模型计费倍率配置表
const MODEL_BILLING_RATES = {
  'ceshi': { // 测试模型
    inputRate: 5,   // 输入倍率
    outputRate: 40  // 输出倍率
  },
  'kelaode': { // 克劳德模型
    inputRate: 10,  // 输入倍率
    outputRate: 55  // 输出倍率
  }
};

/**
 * 计算Token消耗的字数（新倍率计算方式）
 */
const calculateTokenCost = (promptTokens: number, totalTokens: number, modelCode: string): number => {
  // 输出token = 总token - 输入token
  const outputTokens = totalTokens - promptTokens;

  // 获取模型倍率配置，如果模型不存在则使用测试模型的倍率
  const rates = MODEL_BILLING_RATES[modelCode as keyof typeof MODEL_BILLING_RATES] || MODEL_BILLING_RATES['ceshi'];

  // 修正后的倍率计算：输入token * 输入倍率 + 输出token * 输出倍率
  const inputCost = promptTokens * rates.inputRate;
  const outputCost = outputTokens * rates.outputRate;
  const totalCost = inputCost + outputCost;

  console.log(`[计费] 模型${modelCode}: 输入${promptTokens}token×${rates.inputRate}倍=${inputCost}字, 输出${outputTokens}token×${rates.outputRate}倍=${outputCost}字, 总消耗${totalCost}字`);

  return totalCost;
};

/**
 * 获取指定模型的API配置
 */
const getAPIConfig = (modelCode: string) => {
  return API_CONFIGS[modelCode as keyof typeof API_CONFIGS] || API_CONFIGS['ceshi'];
};

/**
 * 创建OpenAI客户端实例
 */
const createOpenAIClient = (modelCode: string): OpenAI => {
  const config = getAPIConfig(modelCode);
  return new OpenAI({
    apiKey: config.apiKey,
    baseURL: config.baseURL,
  });
};

/**
 * POST 方法处理流式AI请求
 */
export async function POST(request: NextRequest) {
  let userUuid: string | null = null;
  let accessToken: string | null = null;
  let actualRequestId: string | null = null;

  // 生成唯一请求ID
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  console.log(`[Stream] 开始处理请求: ${requestId}`);

  try {
    // 解析请求体
    const body: StreamRequest = await request.json();
    const { messages, model, temperature = 0.7, max_tokens = 64000 } = body;

    // 验证请求参数
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: '400' },
        { status: 400 }
      );
    }

    if (!model) {
      return NextResponse.json(
        { error: '400' },
        { status: 400 }
      );
    }

    // 验证用户会话
    const session = await sessionManager.validateAndGetSession(
      request.headers.get('authorization') || ''
    );

    if (!session) {
      console.log(`[Stream] 用户认证失败`);
      return NextResponse.json({ error: '401' }, { status: 401 });
    }

    userUuid = session.userUuid;
    accessToken = session.accessToken;
    console.log(`[Stream] 用户认证成功: ${userUuid}`);

    // 转换模型代号为真实模型名称
    const realModelName = convertModelCodeToName(model);

    console.log(`后端流式生成使用模型: ${model} -> ${realModelName}`);
    console.log("后端发送流式请求:", {
      model: realModelName,
      messages: messages.map(m => ({
        role: m.role,
        content: m.content
      })),
      temperature
    });

    // 处理用户提示词
    const { processedMessages, usedPromptId } = await processUserPrompts(messages, request);

    console.log(`[Stream] 提示词处理结果: usedPromptId=${usedPromptId || '无'}`);

    // 预扣费机制：统计所有消息内容的字符数
    const totalCharacters = processedMessages.reduce((total, message) => {
      return total + (message.content?.length || 0);
    }, 0);

    console.log(`[Stream] 预扣费检查: 预估消耗${totalCharacters}字符`);

    // 预扣费检查：验证用户余额是否足够支付预估消耗（包括模型权限）
    let usesFreeQuota = false;
    if (userUuid && accessToken) {
      const preCheckResult = await BackendBillingService.canUseAI(userUuid, totalCharacters, accessToken, model);
      if (!preCheckResult.canUse) {
        console.log(`[Stream] 预扣费检查失败: 预估${totalCharacters}字符, ${preCheckResult.message}`);
        return NextResponse.json(
          { error: '403' },
          { status: 403 }
        );
      }
      console.log(`[Stream] 预扣费检查通过: 预估${totalCharacters}字符, 当前余额${preCheckResult.balance}字符, 模型: ${model}`);

      // 如果使用免费额度，立即扣除
      if (preCheckResult.usesFreeQuota && model === 'ceshi') {
        console.log(`[Stream] 检测到使用免费额度，立即扣除`);
        const billingResult = await callBillingAPI(userUuid, 1, model, usedPromptId || undefined); // ceshi模型扣除1次免费额度
        if (billingResult.success) {
          console.log(`[Stream] 免费额度扣除成功`);
          usesFreeQuota = true;
        } else {
          console.error(`[Stream] 免费额度扣除失败: ${billingResult.message}`);
          return NextResponse.json(
            { error: '403' },
            { status: 403 }
          );
        }
      }
    }

    // 创建请求
    actualRequestId = sessionManager.createRequest(session.userUuid, model, usedPromptId || undefined, usesFreeQuota);
    if (!actualRequestId) {
      console.error(`[Stream] 创建请求失败，可能达到并发限制`);
      return NextResponse.json({ error: '429' }, { status: 429 }); // 并发限制
    }

    // 创建OpenAI客户端
    const client = createOpenAIClient(model);

    // 调用OpenAI流式API（使用处理后的消息和转换后的真实模型名称）
    const stream = await client.chat.completions.create({
      model: realModelName,
      messages: processedMessages.map(m => ({ role: m.role, content: m.content })),
      temperature,
      max_tokens,
      stream: true,
      stream_options: { include_usage: true }
    });

    console.log("后端Stream created successfully");

    // 创建可读流来转发响应
    const readableStream = new ReadableStream({
      async start(controller) {
        let pendingChars: string[] = []; // 后端字符队列
        let isProcessing = false; // 是否正在处理发送
        let finalUsage: any = null; // 存储最终的usage信息
        let hasBilled = false; // 防止重复计费的标志
        let streamEnded = false; // 标记AI流是否结束
        let controllerClosed = false; // 防止重复关闭控制器

        // 发送最终usage信息和计费的函数
        const sendFinalUsage = async () => {
          // 防止重复调用
          if (controllerClosed) return;
          controllerClosed = true;

          // 如果有usage信息，发送给前端
          if (finalUsage && !hasBilled) {
            // 验证usage数据完整性
            if (finalUsage.prompt_tokens >= 0 &&
                finalUsage.completion_tokens >= 0 &&
                finalUsage.total_tokens >= 0) {

              console.log("流结束，最终usage信息:", finalUsage);

              // 发送usage给前端
              try {
                const encoder = new TextEncoder();
                const usageData = `\n__USAGE_DATA__:${JSON.stringify(finalUsage)}`;
                controller.enqueue(encoder.encode(usageData));
              } catch (error) {
                console.error('发送usage数据失败，控制器可能已关闭:', error);
              }

              // 进行计费（只有在没有使用免费额度的情况下）
              if (userUuid && !usesFreeQuota) {
                hasBilled = true;
                try {
                  const promptTokens = finalUsage.prompt_tokens || 0;
                  const totalTokens = finalUsage.total_tokens || 0;
                  const outputTokens = totalTokens - promptTokens;

                  const totalCost = calculateTokenCost(promptTokens, totalTokens, model);

                  console.log(`最终计费信息: 输入${promptTokens}token, 输出${outputTokens}token(总${totalTokens}-输入${promptTokens}), 总消耗${totalCost}字`);

                  // 调用内部计费API进行扣费，传递模型代号和提示词ID
                  const billingResult = await callBillingAPI(userUuid, totalCost, model, usedPromptId || undefined);
                  if (billingResult.success) {
                    console.log(`最终计费成功`);
                  } else {
                    console.error(`最终计费失败: ${billingResult.message}`);
                  }
                } catch (billingError) {
                  console.error('最终计费处理异常:', billingError);
                }
              } else if (usesFreeQuota) {
                console.log('已使用免费额度，跳过付费计费');
              } else {
                console.log('未获取到用户UUID，跳过计费');
              }
            } else {
              console.error('Usage数据无效:', finalUsage);
            }
          } else if (!hasBilled && !usesFreeQuota && userUuid) {
            // 没有usage信息但需要付费计费的情况（如某些模型不返回usage）
            console.log("流结束，无usage信息，进行预估计费");
            hasBilled = true;
            try {
              // 使用预估的字符数进行计费
              const estimatedCost = Math.max(100, totalCharacters); // 至少100字符
              console.log(`预估计费信息: 预估消耗${estimatedCost}字`);

              // 调用内部计费API进行扣费，传递模型代号和提示词ID
              const billingResult = await callBillingAPI(userUuid, estimatedCost, model, usedPromptId || undefined);
              if (billingResult.success) {
                console.log(`预估计费成功`);
              } else {
                console.error(`预估计费失败: ${billingResult.message}`);
              }
            } catch (billingError) {
              console.error('预估计费处理异常:', billingError);
            }
          } else if (usesFreeQuota) {
            console.log('已使用免费额度，流结束');
          }

          try {
            controller.close();
          } catch (error) {
            console.error('关闭控制器失败:', error);
          }
        };

        // 定时发送字符的函数（模拟前端requestAnimationFrame的节奏）
        const processCharQueue = () => {
          if (pendingChars.length > 0 && !isProcessing) {
            isProcessing = true;

            // 取出一个字符并发送
            const char = pendingChars.shift() as string;
            try {
              const encoder = new TextEncoder();
              controller.enqueue(encoder.encode(char));
            } catch (error) {
              console.error('发送字符失败，控制器可能已关闭:', error);
              return; // 停止处理队列
            }

            isProcessing = false;

            // 继续处理下一个字符（16ms间隔模拟60fps）
            if (pendingChars.length > 0) {
              setTimeout(processCharQueue, 16);
            } else if (streamEnded) {
              // 如果队列空了且流已结束，发送usage数据
              sendFinalUsage();
            }
          }
        };

        try {
          // 处理AI流的chunk
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';

            // 处理普通内容
            if (content) {
              // 将content拆分为字符并加入队列
              for (const char of content) {
                pendingChars.push(char);
              }

              // 如果没有在处理，开始处理队列
              if (!isProcessing && pendingChars.length > 0) {
                processCharQueue();
              }
            }

            // 收集usage信息，使用请求管理器
            if (chunk.usage) {
              finalUsage = {
                prompt_tokens: chunk.usage.prompt_tokens || 0,
                completion_tokens: chunk.usage.completion_tokens || 0,
                total_tokens: chunk.usage.total_tokens || 0
              };
              console.log('收到usage数据:', finalUsage);

              // 更新会话管理器中的usage数据
              if (actualRequestId) {
                sessionManager.updateRequestUsage(actualRequestId, finalUsage);
              }
            }
          }

          streamEnded = true;

          // 使用会话管理器完成请求（正常结束）
          if (actualRequestId) {
            await sessionManager.completeRequest(actualRequestId);
          }

          // 如果队列为空，立即发送usage；否则等队列处理完
          if (pendingChars.length === 0) {
            await sendFinalUsage();
          }

        } catch (error) {
          console.error("后端流式处理错误:", error);

          // 流异常中断，完成请求并扣费
          if (actualRequestId) {
            await sessionManager.completeRequest(actualRequestId);
          }

          if (!controllerClosed) {
            try {
              const errorMessage = handleAIError(error);
              const encoder = new TextEncoder();
              controller.enqueue(encoder.encode(`\n\nERROR: ${errorMessage}`));
              controller.close();
              controllerClosed = true;
            } catch (controllerError) {
              console.error('发送错误信息失败，控制器可能已关闭:', controllerError);
            }
          }
        }
      }
    });

    // 返回流式响应
    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no',
        'Transfer-Encoding': 'chunked',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error: any) {
    console.error("后端API请求错误:", error);

    // 请求异常，完成请求并扣费
    if (actualRequestId) {
      await sessionManager.completeRequest(actualRequestId);
    }

    // 添加更详细的错误信息
    if (error.status) console.error(`错误状态码: ${error.status}`);
    if (error.message) console.error(`错误消息: ${error.message}`);
    if (error.code) console.error(`错误代码: ${error.code}`);
    if (error.type) console.error(`错误类型: ${error.type}`);

    const errorMessage = handleAIError(error);
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
