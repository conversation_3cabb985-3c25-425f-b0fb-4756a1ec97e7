/**
 * AI写作功能压力测试脚本
 * 测试并发请求和计费系统的稳定性
 */

const https = require('https');
const http = require('http');

// 测试配置
const CONFIG = {
  // 服务器配置
  BASE_URL: 'http://localhost:3000',
  API_ENDPOINT: '/api/ai/stream',
  
  // 认证信息 - 从浏览器网络请求中获取的最新JWT Token
  JWT_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.o7BIyZ_OrLVFhKBMnLG928OF9lJHcWsNJd56lA1YeAY',
  USER_UUID: 'e432fabd-c944-4c01-9dcd-2e687287c8dc',
  
  // AI配置
  MODEL: 'kelaode',
  PROMPT_ID: '056c4636-14d7-4eb1-abd0-db58ad305728',
  
  // 测试参数
  CONCURRENT_REQUESTS: 10,        // 并发请求数量
  REQUEST_INTERVAL: 100,          // 请求间隔(毫秒)
  INTERRUPT_DELAY: 2000,          // 中断延迟(毫秒)
  INTERRUPT_PROBABILITY: 0.3,     // 中断概率(30%)
  
  // 测试内容
  TEST_MESSAGES: [
    '生成3000字小说',
    '写一个悬疑故事',
    '创作科幻小说片段',
    '编写爱情故事',
    '写一个武侠小说章节'
  ]
};

// 全局统计
const STATS = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  interruptedRequests: 0,
  totalTokens: 0,
  totalCost: 0,
  requestTimes: [],
  errors: []
};

/**
 * 生成唯一请求ID
 */
function generateRequestId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `req_${timestamp}_${random}`;
}

/**
 * 创建AI请求的消息结构
 */
function createRequestPayload(message, requestId) {
  return {
    messages: [
      {
        role: 'system',
        content: `__USER_PROMPT_ID__:${CONFIG.PROMPT_ID}`
      },
      {
        role: 'user',
        content: message
      }
    ],
    model: CONFIG.MODEL,
    temperature: 0.7,
    max_tokens: 64000
  };
}

/**
 * 发送AI请求
 */
function sendAIRequest(message, requestId, shouldInterrupt = false) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const payload = createRequestPayload(message, requestId);
    const postData = JSON.stringify(payload);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: CONFIG.API_ENDPOINT,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`,
        'Referer': 'http://localhost:3000/works/1754363667903',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
      }
    };

    console.log(`🚀 [${requestId}] 发送请求: "${message}"`);
    
    const req = http.request(options, (res) => {
      let responseData = '';
      let usageData = null;
      let interrupted = false;
      
      // 设置中断定时器
      let interruptTimer = null;
      if (shouldInterrupt) {
        interruptTimer = setTimeout(() => {
          console.log(`⚡ [${requestId}] 模拟中断请求`);
          interrupted = true;
          req.destroy();
          STATS.interruptedRequests++;
        }, CONFIG.INTERRUPT_DELAY);
      }
      
      res.on('data', (chunk) => {
        responseData += chunk.toString();
        
        // 解析usage数据
        const usageMatch = responseData.match(/__USAGE_DATA__:({.*?})/);
        if (usageMatch) {
          try {
            usageData = JSON.parse(usageMatch[1]);
          } catch (e) {
            // 忽略解析错误
          }
        }
      });
      
      res.on('end', () => {
        if (interruptTimer) clearTimeout(interruptTimer);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        if (!interrupted) {
          STATS.successfulRequests++;
          STATS.requestTimes.push(duration);
          
          if (usageData) {
            STATS.totalTokens += usageData.total_tokens || 0;
            // 根据模型计算费用 (kelaode: 输入×10 + 输出×55)
            const inputCost = (usageData.prompt_tokens || 0) * 10;
            const outputCost = (usageData.completion_tokens || 0) * 55;
            const totalCost = inputCost + outputCost;
            STATS.totalCost += totalCost;
            
            console.log(`✅ [${requestId}] 完成 - 耗时: ${duration}ms, Tokens: ${usageData.total_tokens}, 费用: ${totalCost}字符`);
          } else {
            console.log(`✅ [${requestId}] 完成 - 耗时: ${duration}ms (无usage数据)`);
          }
          
          resolve({
            requestId,
            duration,
            success: true,
            interrupted: false,
            usage: usageData,
            responseLength: responseData.length
          });
        }
      });
      
      res.on('error', (error) => {
        if (interruptTimer) clearTimeout(interruptTimer);
        
        if (!interrupted) {
          STATS.failedRequests++;
          STATS.errors.push({ requestId, error: error.message });
          console.log(`❌ [${requestId}] 失败: ${error.message}`);
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      if (!interrupted) {
        STATS.failedRequests++;
        STATS.errors.push({ requestId, error: error.message });
        console.log(`❌ [${requestId}] 请求错误: ${error.message}`);
        reject(error);
      }
    });
    
    req.write(postData);
    req.end();
  });
}

/**
 * 执行压力测试
 */
async function runStressTest() {
  console.log('🎯 开始AI写作功能压力测试');
  console.log(`📊 测试配置:`);
  console.log(`   - 并发请求数: ${CONFIG.CONCURRENT_REQUESTS}`);
  console.log(`   - 请求间隔: ${CONFIG.REQUEST_INTERVAL}ms`);
  console.log(`   - 中断概率: ${CONFIG.INTERRUPT_PROBABILITY * 100}%`);
  console.log(`   - 模型: ${CONFIG.MODEL}`);
  console.log(`   - 提示词ID: ${CONFIG.PROMPT_ID}`);
  console.log('');
  
  const startTime = Date.now();
  const promises = [];
  
  // 创建并发请求
  for (let i = 0; i < CONFIG.CONCURRENT_REQUESTS; i++) {
    const requestId = generateRequestId();
    const message = CONFIG.TEST_MESSAGES[i % CONFIG.TEST_MESSAGES.length];
    const shouldInterrupt = Math.random() < CONFIG.INTERRUPT_PROBABILITY;
    
    STATS.totalRequests++;
    
    // 添加请求间隔
    if (i > 0) {
      await new Promise(resolve => setTimeout(resolve, CONFIG.REQUEST_INTERVAL));
    }
    
    const promise = sendAIRequest(message, requestId, shouldInterrupt)
      .catch(error => ({
        requestId,
        success: false,
        error: error.message
      }));
    
    promises.push(promise);
  }
  
  // 等待所有请求完成
  console.log('⏳ 等待所有请求完成...\n');
  const results = await Promise.allSettled(promises);
  
  const endTime = Date.now();
  const totalDuration = endTime - startTime;
  
  // 生成测试报告
  generateReport(totalDuration, results);
}

/**
 * 生成测试报告
 */
function generateReport(totalDuration, results) {
  console.log('\n' + '='.repeat(60));
  console.log('📈 压力测试报告');
  console.log('='.repeat(60));
  
  console.log(`🕒 总耗时: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}秒)`);
  console.log(`📊 请求统计:`);
  console.log(`   - 总请求数: ${STATS.totalRequests}`);
  console.log(`   - 成功请求: ${STATS.successfulRequests} (${(STATS.successfulRequests / STATS.totalRequests * 100).toFixed(1)}%)`);
  console.log(`   - 失败请求: ${STATS.failedRequests} (${(STATS.failedRequests / STATS.totalRequests * 100).toFixed(1)}%)`);
  console.log(`   - 中断请求: ${STATS.interruptedRequests} (${(STATS.interruptedRequests / STATS.totalRequests * 100).toFixed(1)}%)`);
  
  if (STATS.requestTimes.length > 0) {
    const avgTime = STATS.requestTimes.reduce((a, b) => a + b, 0) / STATS.requestTimes.length;
    const minTime = Math.min(...STATS.requestTimes);
    const maxTime = Math.max(...STATS.requestTimes);
    
    console.log(`⏱️  响应时间:`);
    console.log(`   - 平均响应时间: ${avgTime.toFixed(0)}ms`);
    console.log(`   - 最快响应: ${minTime}ms`);
    console.log(`   - 最慢响应: ${maxTime}ms`);
  }
  
  console.log(`💰 计费统计:`);
  console.log(`   - 总消耗Token: ${STATS.totalTokens.toLocaleString()}`);
  console.log(`   - 总费用: ${STATS.totalCost.toLocaleString()}字符`);
  console.log(`   - 平均每请求费用: ${STATS.successfulRequests > 0 ? Math.round(STATS.totalCost / STATS.successfulRequests).toLocaleString() : 0}字符`);
  
  if (STATS.errors.length > 0) {
    console.log(`❌ 错误详情:`);
    STATS.errors.forEach(error => {
      console.log(`   - [${error.requestId}]: ${error.error}`);
    });
  }
  
  console.log('\n🎯 测试结论:');
  if (STATS.successfulRequests / STATS.totalRequests >= 0.8) {
    console.log('✅ 系统表现良好，能够处理高并发请求');
  } else if (STATS.successfulRequests / STATS.totalRequests >= 0.6) {
    console.log('⚠️  系统表现一般，存在一些稳定性问题');
  } else {
    console.log('❌ 系统表现较差，需要优化并发处理能力');
  }
  
  if (STATS.totalCost > 0) {
    console.log('💳 计费系统正常工作，能够正确统计费用');
  }
  
  console.log('='.repeat(60));
}

// 启动测试
if (require.main === module) {
  runStressTest().catch(console.error);
}

module.exports = { runStressTest, CONFIG, STATS };
