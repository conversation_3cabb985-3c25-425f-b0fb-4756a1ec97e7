/**
 * 专用计费API端点
 * 只允许服务器端内部调用，用于扣除用户字数余额
 * ⚠️ 此API不允许前端直接访问
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 硬编码内部API密钥 - 只有服务器端代码知道此密钥
const INTERNAL_API_KEY = "billing_internal_2024_secure_key_xyz789";

// 创建Supabase客户端
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 计费结果接口
export interface BillingResult {
  success: boolean;
  message?: string;
}

// 请求体接口
interface DeductRequest {
  internalApiKey: string;
  userUuid: string;
  amount: number;
  modelCode?: string; // 模型代号，用于判断是否为免费版模型
  promptId?: string; // 提示词ID，用于为作者添加奖励额度
}

/**
 * ⚠️ 已删除免费模型机制
 * 所有模型现在都需要付费使用
 */
const isFreeModel = (modelCode?: string): boolean => {
  return false; // 所有模型都需要付费
};

/**
 * 执行扣费操作（支持免费额度）
 */
export const executeDeduction = async (
  userUuid: string,
  amount: number,
  modelCode?: string,
  promptId?: string
): Promise<BillingResult> => {
  try {
    console.log(`[Billing API] 开始扣费 - 用户: ${userUuid}, 模型: ${modelCode}, 金额: ${amount}`);

    // 检查是否为ceshi模型且用户有免费额度或奖励额度
    if (modelCode === 'ceshi') {
      console.log(`[Billing API] 检测到ceshi模型，尝试使用免费额度和奖励额度`);

      // 尝试扣除免费额度和奖励额度
      const { data: freeDeductResult, error: freeError } = await supabase
        .rpc('deduct_daily_free_quota', {
          user_uuid: userUuid,
          deduct_amount: 1, // ceshi模型每次使用扣除1次额度
          access_password: 'PROMPT_ACCESS_2024_SECURE_KEY'
        });

      console.log(`[Billing API] 免费/奖励额度扣除结果: data=${JSON.stringify(freeDeductResult)}, error=${freeError?.message || 'none'}`);

      if (!freeError && freeDeductResult && freeDeductResult.success) {
        console.log(`[Billing API] 免费/奖励额度扣除成功`);
        console.log(`[Billing API] 扣除详情: 每日免费=${freeDeductResult.daily_deducted}, 奖励=${freeDeductResult.reward_deducted}`);

        // 只有使用了每日免费额度时，才为作者添加奖励额度
        if (freeDeductResult.used_daily && promptId) {
          console.log(`[Billing API] 使用了每日免费额度，检测到提示词使用，尝试为作者添加奖励额度`);
          console.log(`[Billing API] 参数: 使用者UUID=${userUuid}, 提示词ID=${promptId}`);
          try {
            const { data: rewardResult, error: rewardError } = await supabase
              .rpc('add_author_daily_quota_for_prompt_usage', {
                user_uuid_param: userUuid,
                prompt_id_param: promptId,
                access_password: 'PROMPT_ACCESS_2024_SECURE_KEY'
              });

            console.log(`[Billing API] 奖励函数返回: data=${rewardResult}, error=${rewardError?.message || 'none'}`);

            if (!rewardError && rewardResult) {
              console.log(`[Billing API] 成功为提示词作者添加奖励额度`);
            } else {
              console.log(`[Billing API] 未能为作者添加奖励额度: ${rewardError?.message || '函数返回false，可能是自己使用自己的提示词或其他原因'}`);
            }
          } catch (rewardError) {
            console.error(`[Billing API] 添加作者奖励额度异常:`, rewardError);
          }
        } else if (freeDeductResult.used_reward) {
          console.log(`[Billing API] 使用了奖励额度，不给作者添加奖励（避免循环）`);
        } else {
          console.log(`[Billing API] 未提供提示词ID或未使用每日免费额度，跳过作者奖励`);
        }

        return {
          success: true,
          message: `使用${freeDeductResult.used_daily ? '每日免费' : ''}${freeDeductResult.used_reward ? '奖励' : ''}额度成功`
        };
      } else {
        console.log(`[Billing API] 免费/奖励额度不足或扣除失败，转为付费扣费`);
      }
    } else {
      console.log(`[Billing API] 非ceshi模型 (${modelCode})，直接使用付费扣费`);
    }

    // 免费额度不足或非ceshi模型，使用付费扣费
    console.log(`[Billing API] 模型 ${modelCode}：执行付费扣费`);
    const { data, error } = await supabase
      .rpc('deduct_user_word_count', {
        user_uuid: userUuid,
        deduct_amount: amount,
        access_password: 'PROMPT_ACCESS_2024_SECURE_KEY'
      });

    if (error || !data) {
      return {
        success: false,
        message: `扣费失败: ${error?.message || '未知错误'}`
      };
    }

    // 付费扣费成功后，如果提供了提示词ID，也为作者添加奖励额度
    if (promptId) {
      console.log(`[Billing API] 付费扣费成功，检测到提示词使用，尝试为作者添加奖励额度`);
      console.log(`[Billing API] 参数: 使用者UUID=${userUuid}, 提示词ID=${promptId}`);
      try {
        const { data: rewardResult, error: rewardError } = await supabase
          .rpc('add_author_daily_quota_for_prompt_usage', {
            user_uuid_param: userUuid,
            prompt_id_param: promptId,
            access_password: 'PROMPT_ACCESS_2024_SECURE_KEY'
          });

        console.log(`[Billing API] 奖励函数返回（付费模式）: data=${rewardResult}, error=${rewardError?.message || 'none'}`);

        if (!rewardError && rewardResult) {
          console.log(`[Billing API] 成功为提示词作者添加奖励额度（付费模式）`);
        } else {
          console.log(`[Billing API] 未能为作者添加奖励额度（付费模式）: ${rewardError?.message || '函数返回false，可能是自己使用自己的提示词或其他原因'}`);
        }
      } catch (rewardError) {
        console.error(`[Billing API] 添加作者奖励额度异常（付费模式）:`, rewardError);
      }
    } else {
      console.log(`[Billing API] 未提供提示词ID，跳过作者奖励（付费模式）`);
    }

    return {
      success: true,
      message: '付费扣费成功'
    };
  } catch (error) {
    console.error('扣费异常:', error);
    return {
      success: false,
      message: '扣费异常'
    };
  }
};

/**
 * POST 方法 - 处理扣费请求
 * 只允许服务器端内部调用
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body: DeductRequest = await request.json();
    const { internalApiKey, userUuid, amount, modelCode, promptId } = body;

    // 唯一的安全验证：内部API密钥验证（后端token验证）
    if (!internalApiKey || internalApiKey !== INTERNAL_API_KEY) {
      console.error('[Billing API] 无效的内部API密钥访问尝试');
      return NextResponse.json(
        { error: '无效的内部API密钥' },
        { status: 403 }
      );
    }

    // 基本参数验证
    if (!userUuid || typeof amount !== 'number' || amount <= 0) {
      console.error('[Billing API] 无效的请求参数');
      return NextResponse.json(
        { error: '无效的请求参数' },
        { status: 400 }
      );
    }

    console.log(`[Billing API] 开始处理扣费请求 - 用户: ${userUuid}, 金额: ${amount}, 模型: ${modelCode || '未指定'}, 提示词: ${promptId || '无'}`);

    // 执行扣费操作
    const result = await executeDeduction(userUuid, amount, modelCode, promptId);

    if (result.success) {
      console.log(`[Billing API] 扣费成功`);
    } else {
      console.error(`[Billing API] 扣费失败 - ${result.message}`);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('[Billing API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 其他HTTP方法不被支持
 */
export async function GET() {
  return NextResponse.json(
    { error: '此端点不支持GET请求' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: '此端点不支持PUT请求' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: '此端点不支持DELETE请求' },
    { status: 405 }
  );
}
