/**
 * 会话管理统计API
 * 提供系统会话和请求的统计信息
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 导入会话管理器
import { sessionManager } from '../../ai/stream/route';

interface SessionStats {
  totalSessions: number;
  totalRequests: number;
  activeRequests: number;
  billingQueue: number;
  memoryUsage: {
    sessions: number;
    requests: number;
  };
}

/**
 * GET - 获取会话统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未提供认证令牌' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return NextResponse.json({ error: '用户未登录' }, { status: 401 });
    }

    // 检查管理员权限（这里简化处理，实际应该检查用户角色）
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(user.email || '')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    // 获取会话统计信息
    const stats = sessionManager.getStats();

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      stats
    });

  } catch (error) {
    console.error('获取会话统计失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * POST - 手动触发清理操作
 */
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未提供认证令牌' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return NextResponse.json({ error: '用户未登录' }, { status: 401 });
    }

    // 检查管理员权限
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(user.email || '')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    // 手动触发清理
    sessionManager.cleanup();

    return NextResponse.json({
      success: true,
      message: '清理操作已执行',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('执行清理操作失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
