/**
 * 会话管理系统测试脚本
 * 测试新的SimpleSessionManager功能
 */

const http = require('http');

// 配置
const CONFIG = {
  HOST: 'localhost',
  PORT: 3000,
  // 请替换为有效的JWT Token
  JWT_TOKEN: '在这里粘贴你从浏览器获取的最新JWT Token',
  CONCURRENT_REQUESTS: 5,
  TEST_MESSAGE: '请简单介绍一下人工智能的发展历史'
};

// 发送AI请求
async function sendAIRequest(message, requestId) {
  return new Promise((resolve) => {
    const postData = JSON.stringify({
      messages: [
        {
          role: 'user',
          content: message
        }
      ],
      model: 'ceshi',
      temperature: 0.7,
      max_tokens: 100
    });

    const options = {
      hostname: CONFIG.HOST,
      port: CONFIG.PORT,
      path: '/api/ai/stream',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`,
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`🚀 发送请求 ${requestId}...`);
    const startTime = Date.now();

    const req = http.request(options, (res) => {
      let responseData = '';
      let tokenCount = 0;

      console.log(`📡 请求 ${requestId} 状态码: ${res.statusCode}`);

      res.on('data', (chunk) => {
        responseData += chunk.toString();
        tokenCount += chunk.length;
      });

      res.on('end', () => {
        const duration = Date.now() - startTime;
        
        resolve({
          requestId,
          success: res.statusCode === 200,
          statusCode: res.statusCode,
          duration,
          tokenCount,
          responseLength: responseData.length,
          hasUsage: responseData.includes('usage'),
          error: res.statusCode !== 200 ? responseData : null
        });

        console.log(`✅ 请求 ${requestId} 完成: ${duration}ms, ${tokenCount} tokens`);
      });
    });

    req.on('error', (error) => {
      const duration = Date.now() - startTime;
      console.error(`❌ 请求 ${requestId} 失败:`, error.message);
      
      resolve({
        requestId,
        success: false,
        duration,
        error: error.message
      });
    });

    req.write(postData);
    req.end();
  });
}

// 获取会话统计信息
async function getSessionStats() {
  return new Promise((resolve) => {
    const options = {
      hostname: CONFIG.HOST,
      port: CONFIG.PORT,
      path: '/api/admin/session-stats',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CONFIG.JWT_TOKEN}`
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const stats = JSON.parse(data);
          resolve({
            success: res.statusCode === 200,
            stats: stats.stats || null,
            error: res.statusCode !== 200 ? data : null
          });
        } catch (error) {
          resolve({
            success: false,
            error: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });

    req.end();
  });
}

// 主测试函数
async function testSessionManager() {
  console.log('🧪 开始测试会话管理系统');
  console.log('='.repeat(50));

  // 1. 获取初始统计信息
  console.log('\n📊 获取初始会话统计...');
  const initialStats = await getSessionStats();
  if (initialStats.success) {
    console.log('初始统计:', JSON.stringify(initialStats.stats, null, 2));
  } else {
    console.log('获取统计失败:', initialStats.error);
  }

  // 2. 并发测试
  console.log(`\n🚀 开始并发测试 (${CONFIG.CONCURRENT_REQUESTS}个请求)...`);
  const startTime = Date.now();

  const promises = Array.from({ length: CONFIG.CONCURRENT_REQUESTS }, (_, i) => 
    sendAIRequest(CONFIG.TEST_MESSAGE, `test_${i + 1}`)
  );

  const results = await Promise.all(promises);
  const totalDuration = Date.now() - startTime;

  // 3. 分析结果
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;

  console.log('\n📈 测试结果:');
  console.log(`   总请求数: ${results.length}`);
  console.log(`   成功: ${successful}`);
  console.log(`   失败: ${failed}`);
  console.log(`   成功率: ${(successful / results.length * 100).toFixed(1)}%`);
  console.log(`   平均响应时间: ${avgDuration.toFixed(0)}ms`);
  console.log(`   总耗时: ${totalDuration}ms`);

  // 4. 显示失败详情
  if (failed > 0) {
    console.log('\n❌ 失败请求详情:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`   ${r.requestId}: ${r.error || `HTTP ${r.statusCode}`}`);
    });
  }

  // 5. 等待一段时间后获取最终统计
  console.log('\n⏳ 等待3秒后获取最终统计...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  const finalStats = await getSessionStats();
  if (finalStats.success) {
    console.log('最终统计:', JSON.stringify(finalStats.stats, null, 2));
  } else {
    console.log('获取最终统计失败:', finalStats.error);
  }

  console.log('\n' + '='.repeat(50));
  console.log('🎯 测试完成!');
}

// 运行测试
if (require.main === module) {
  testSessionManager().catch(console.error);
}

module.exports = {
  testSessionManager,
  sendAIRequest,
  getSessionStats
};
