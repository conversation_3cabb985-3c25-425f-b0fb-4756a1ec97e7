'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { getAllWorks, addWork, updateWork, deleteWork, Work } from '@/data';

import TopBar from '@/components/TopBar';
import { DropdownMenu, DropdownMenuItem } from '@/components/common/DropdownMenu';

import { workContentUtils } from '@/lib/utils';
import { Chapter } from '@/types/chapter';
import Encoding from 'encoding-japanese';
import { TextDecoder as TextDecoderPolyfill } from 'text-encoding';

// 中文数字转换函数
function convertChineseToNumber(chineseStr: string): number {
  const chineseDigits: { [key: string]: number } = {
    '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4, '五': 5,
    '六': 6, '七': 7, '八': 8, '九': 9
  };

  const chineseUnits: { [key: string]: number } = {
    '十': 10, '百': 100, '千': 1000, '万': 10000
  };

  // 处理简单的单个数字
  if (chineseDigits[chineseStr] !== undefined) {
    return chineseDigits[chineseStr];
  }

  // 处理"十"
  if (chineseStr === '十') {
    return 10;
  }

  let result = 0;
  let currentNum = 0;
  let hasDigit = false;

  for (let i = 0; i < chineseStr.length; i++) {
    const char = chineseStr[i];

    if (chineseDigits[char] !== undefined) {
      currentNum = chineseDigits[char];
      hasDigit = true;
    } else if (chineseUnits[char] !== undefined) {
      const unit = chineseUnits[char];

      if (unit === 10000) { // 万
        result = (result + currentNum) * unit;
        currentNum = 0;
        hasDigit = false;
      } else if (unit === 1000) { // 千
        if (!hasDigit) currentNum = 1;
        result += currentNum * unit;
        currentNum = 0;
        hasDigit = false;
      } else if (unit === 100) { // 百
        if (!hasDigit) currentNum = 1;
        result += currentNum * unit;
        currentNum = 0;
        hasDigit = false;
      } else if (unit === 10) { // 十
        if (!hasDigit) currentNum = 1;
        result += currentNum * unit;
        currentNum = 0;
        hasDigit = false;
      }
    }
  }

  result += currentNum;
  return result > 0 ? result : -1;
}

// 添加GBK/GB18030转UTF-8的函数
function convertChineseEncoding(buffer: ArrayBuffer): string {
  try {
    // 转换为Uint8Array以便处理
    const uint8Array = new Uint8Array(buffer);

    // 首先检查是否是UTF-8 BOM
    if (uint8Array.length >= 3 &&
        uint8Array[0] === 0xEF &&
        uint8Array[1] === 0xBB &&
        uint8Array[2] === 0xBF) {
      // UTF-8带BOM
      console.log('检测到UTF-8 BOM');
      return new TextDecoder('utf-8').decode(uint8Array.slice(3));
    }

    // 尝试使用浏览器原生TextDecoder解码UTF-8
    let utf8Text = '';
    try {
      utf8Text = new TextDecoder('utf-8').decode(uint8Array);
      // 如果UTF-8解码没有明显乱码，直接返回
      if (!utf8Text.includes('\uFFFD') && !utf8Text.includes('锟斤拷')) {
        console.log('UTF-8解码成功，无明显乱码');
        return utf8Text;
      }
    } catch (e) {
      console.warn('UTF-8解码失败', e);
    }

    // 尝试检测编码
    const detected = Encoding.detect(uint8Array);
    console.log('Encoding-Japanese检测到的编码:', detected);

    // 尝试GBK解码 - 使用text-encoding库的polyfill
    try {
      console.log('尝试GBK解码');
      // 注意：浏览器原生不支持GBK，使用polyfill
      const gbkText = new TextDecoderPolyfill('gbk').decode(uint8Array);

      // 检查是否有明显乱码
      const gbkFffdCount = (gbkText.match(/\uFFFD/g) || []).length;
      const utf8FffdCount = (utf8Text.match(/\uFFFD/g) || []).length;

      console.log('GBK解码乱码数:', gbkFffdCount, 'UTF-8解码乱码数:', utf8FffdCount);

      // 如果GBK解码的乱码更少，使用GBK解码结果
      if (gbkFffdCount < utf8FffdCount) {
        console.log('使用GBK解码结果');
        return gbkText;
      }
    } catch (gbkError) {
      console.warn('GBK polyfill解码失败', gbkError);
    }

    // 使用Encoding-Japanese库进行转换尝试
    try {
      console.log('尝试使用Encoding-Japanese转换');
      // 尝试使用检测到的编码，如果是BINARY（无法检测）则尝试GBK
      const fromEncoding = detected === 'BINARY' ? 'GBK' : detected;

      const unicodeArray = Encoding.convert(uint8Array, {
        to: 'UNICODE',
        from: fromEncoding
      });

      const convertedText = Encoding.codeToString(unicodeArray);

      // 检查转换结果中的乱码
      const convertedFffdCount = (convertedText.match(/\uFFFD/g) || []).length;
      const utf8FffdCount = (utf8Text.match(/\uFFFD/g) || []).length;

      console.log('Encoding-Japanese转换乱码数:', convertedFffdCount, 'UTF-8解码乱码数:', utf8FffdCount);

      // 如果是中文相关编码或转换后乱码更少，使用转换结果
      if (['GBK', 'GB18030', 'GB2312', 'BIG5', 'CHINESE'].includes(detected) ||
          convertedFffdCount < utf8FffdCount) {
        console.log('使用Encoding-Japanese转换结果');
        return convertedText;
      }
    } catch (encError) {
      console.warn('Encoding-Japanese转换失败', encError);
    }

    // 如果上述所有方法都失败或效果不理想，回退到UTF-8
    console.log('所有方法效果不佳，回退到UTF-8解码结果');
    return utf8Text;
  } catch (error) {
    console.error('编码转换总体错误:', error);
    // 最终兜底方案
    return new TextDecoder('utf-8').decode(buffer);
  }
}

export default function WorksPage() {
  const [works, setWorks] = useState<Work[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentWorkId, setCurrentWorkId] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
  });
  const [error, setError] = useState('');
  const router = useRouter();

  // 添加删除确认弹窗状态
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [workToDelete, setWorkToDelete] = useState<number | null>(null);

  // 添加导入TXT相关状态
  const [fileUploadMode, setFileUploadMode] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [chapterPreview, setChapterPreview] = useState<Chapter[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  // 添加过滤和排序相关状态
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('updated');
  const [searchQuery, setSearchQuery] = useState('');

  // 添加弹窗状态
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);



  useEffect(() => {
    const fetchWorks = async () => {
      try {
        const allWorks = await getAllWorks();
        setWorks(allWorks);
      } catch (error) {
        console.error('获取作品失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorks();
  }, []);

  const handleCreateWork = () => {
    setIsEditMode(false);
    setCurrentWorkId(null);
    setFormData({
      title: '',
    });
    // 不重置fileUploadMode，保留当前状态
    setUploadedFile(null);
    setChapterPreview([]);
    setShowPreview(false);
    setIsModalOpen(true);
  };

  const handleEditWork = (work: Work) => {
    setIsEditMode(true);
    setCurrentWorkId(work.id ?? null);
    setFormData({
      title: work.title,
    });
    setIsModalOpen(true);
  };

  const handleDeleteWork = async (id: number) => {
    setWorkToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (!workToDelete) return;

    setIsDeleting(true);
    try {
      // 获取要删除的作品信息
      const workToDeleteInfo = works.find(w => w.id === workToDelete);

      // 1. 删除本地作品
      await deleteWork(workToDelete);

      // 2. 删除云端文件（如果存在）
      if (workToDeleteInfo) {
        try {
          const response = await fetch('/api/files/delete', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ workTitle: workToDeleteInfo.title })
          });

          if (!response.ok) {
            console.warn('云端文件删除失败，可能文件不存在');
          } else {
            console.log(`作品 "${workToDeleteInfo.title}" 已从云端删除`);
          }
        } catch (cloudError) {
          console.warn('删除云端文件时出错:', cloudError);
        }
      }

      // 更新作品列表
      const allWorks = await getAllWorks();
      setWorks(allWorks);
    } catch (error) {
      console.error('删除作品失败:', error);
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setWorkToDelete(null);
    }
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
    setWorkToDelete(null);
  };

  // 导出TXT功能
  const handleExportTxt = (work: Work) => {
    try {
      // 解析章节数据
      const chapters = workContentUtils.parseContent(work.content);

      // 按order字段排序
      const sortedChapters = chapters.sort((a, b) => a.order - b.order);

      // 格式化输出内容
      let txtContent = '';
      sortedChapters.forEach((chapter, index) => {
        // 添加章节标题
        txtContent += chapter.title + '\n';

        // 添加章节内容
        if (chapter.content.trim()) {
          txtContent += chapter.content + '\n';
        }

        // 章节间添加空行（除了最后一章）
        if (index < sortedChapters.length - 1) {
          txtContent += '\n';
        }
      });

      // 创建Blob对象
      const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 处理文件名，移除特殊字符
      const fileName = work.title.replace(/[<>:"/\\|?*]/g, '_') + '.txt';
      link.download = fileName;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);

      console.log(`导出TXT成功: ${fileName}`);
    } catch (error) {
      console.error('导出TXT失败:', error);
      alert('导出TXT失败，请重试');
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setError('');
    setFormData({
      title: '',
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      if (!formData.title.trim()) {
        throw new Error('标题不能为空');
      }

      const now = new Date();

      // Prepare data, adding back empty/default values for removed fields for DB compatibility
      const workData = {
        title: formData.title,
        description: '', // Default empty string
        type: 'novel' as 'novel' | 'character' | 'worldbuilding' | 'plot', // Default type
        content: '', // Default empty string
      };

      if (isEditMode && currentWorkId) {
        // 更新现有作品
        // Note: Editing might need a different approach if fields are truly removed later
        await updateWork({
          ...workData,
          id: currentWorkId,
          updatedAt: now,
          createdAt: works.find(w => w.id === currentWorkId)?.createdAt || now
        });
      } else {
        // 创建新作品
        const newWork = await addWork({
          ...workData,
          createdAt: now,
          updatedAt: now
        });

        // 如果是导入TXT模式且有章节，则直接跳转到作品详情页
        if (fileUploadMode && chapterPreview.length > 0) {
          // 保存作品内容（包含章节信息）
          await updateWork({
            ...newWork,
            content: JSON.stringify(chapterPreview),
            updatedAt: new Date()
          });

          router.push(`/works/${newWork.id}`);
          return;
        }
      }

      // 重新获取作品列表
      const allWorks = await getAllWorks();
      setWorks(allWorks);

      // 重置表单并关闭创建界面
      setFormData({
        title: '',
      });
      setIsModalOpen(false);
      setFileUploadMode(false);
      setUploadedFile(null);
      setChapterPreview([]);
      setShowPreview(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : isEditMode ? '更新作品失败' : '创建作品失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理TXT文件上传
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];
    console.log('文件信息:', file.name, file.size, file.type);

    // 检查文件类型
    if (file.type !== 'text/plain' && !file.name.toLowerCase().endsWith('.txt')) {
      setError('请上传TXT文本文件');
      return;
    }

    // 检查文件大小限制（20MB = 20 * 1024 * 1024 字节）
    const maxFileSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxFileSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
      setError(`文件过大！当前文件大小为 ${fileSizeMB}MB，最大支持 20MB。请压缩文件或分割后重试。`);
      return;
    }

    setUploading(true);
    setUploadedFile(file);
    setError('');

    try {
      // 设置作品标题为文件名（去除.txt后缀）
      const fileName = file.name.replace(/\.txt$/i, '');
      setFormData({
        title: fileName
      });

      // 读取文件内容
      let fileBuffer = await file.arrayBuffer();
      console.log('文件大小:', fileBuffer.byteLength, '字节');

      // 检查文件的前几个字节，帮助判断编码
      const dataView = new DataView(fileBuffer);
      let hexHeader = '';
      for (let i = 0; i < Math.min(fileBuffer.byteLength, 8); i++) {
        hexHeader += dataView.getUint8(i).toString(16).padStart(2, '0') + ' ';
      }
      console.log('文件头部字节:', hexHeader);

      let text = '';

      // 尝试处理不同的编码
      try {
        // 使用改进的编码检测和转换
        console.log('开始尝试解码文件');
        text = convertChineseEncoding(fileBuffer);
        console.log('解码完成，结果长度:', text.length);

        // 检查解码结果中的乱码数量
        const fffdCount = (text.match(/\uFFFD/g) || []).length;
        if (fffdCount > 0) {
          console.warn(`解码后仍有 ${fffdCount} 个乱码字符`);
        }

        // 如果文本中仍包含明显的乱码特征，记录警告
        if (text.includes('\uFFFD') || text.includes('锟斤拷')) {
          console.warn('文本中可能存在编码问题，尝试手动转换');
          // 尝试处理常见的乱码字符
          text = text.replace(/锟斤拷/g, '').replace(/\uFFFD/g, '');
        }
      } catch (err) {
        console.error('编码转换失败:', err);
        // 兜底使用UTF-8
        text = new TextDecoder('utf-8').decode(fileBuffer);
      }

      // 过滤掉常见的电子书水印和版权声明
      const cleanText = text.replace(/☆[^☆]*?看帮网[^☆]*?☆/g, '').replace(/☆本文由.*?所有☆/g, '').replace(/☆请勿用于商业.*?自负☆/g, '').replace(/☆https?:\/\/www\.kanbang\.cc☆/g, '');

      // 提取文本中的一小部分用于检查
      const textSample = text.substring(0, 200);
      console.log('文本样本:', textSample);

      // 解析章节
      console.log('开始解析章节');
      const chapters = parseChapters(cleanText);
      console.log('章节解析完成，识别到', chapters.length, '个章节');

      if (chapters.length === 0) {
        // 如果未识别到章节，创建单个章节
        console.log('未识别到章节，使用整个文本作为单章节');
        setChapterPreview([
          {
            order: 1,
            title: '第一章',
            content: text
          }
        ]);
      } else {
        console.log('章节标题示例:', chapters.slice(0, 3).map(ch => ch.title));
        setChapterPreview(chapters);
      }

      setShowPreview(true);
    } catch (err) {
      setError('文件解析失败，请重试');
      console.error('文件解析错误:', err);
    } finally {
      setUploading(false);
    }
  };

  // 章节连续性检测函数 - 严格的前后连续性验证
  const filterChaptersByContinuity = (matches: { title: string, index: number }[]): { title: string, index: number }[] => {
    if (matches.length <= 1) return matches;

    // 提取章节编号
    const chaptersWithNumbers = matches.map(match => {
      // 尝试提取数字编号
      const numberMatch = match.title.match(/第(\d+)章/);
      if (numberMatch) {
        return {
          ...match,
          number: parseInt(numberMatch[1], 10)
        };
      }

      // 尝试提取中文数字编号
      const chineseMatch = match.title.match(/第([零一二两三四五六七八九十百千万]+)章/);
      if (chineseMatch) {
        const chineseNumber = chineseMatch[1];
        const convertedNumber = convertChineseToNumber(chineseNumber);
        if (convertedNumber > 0) {
          return {
            ...match,
            number: convertedNumber
          };
        }
      }

      return { ...match, number: -1 }; // 无法识别编号
    }).filter(item => item.number > 0); // 过滤掉无法识别编号的章节

    if (chaptersWithNumbers.length <= 1) return matches;

    // 按原始位置排序（保持在文本中的顺序）
    chaptersWithNumbers.sort((a, b) => a.index - b.index);

    // 检测所有连续段
    const continuousSegments: typeof chaptersWithNumbers[] = [];
    let currentSegment: typeof chaptersWithNumbers = [];

    for (let i = 0; i < chaptersWithNumbers.length; i++) {
      const current = chaptersWithNumbers[i];

      // 如果是第一个章节，或者编号是连续的，加入当前段
      if (currentSegment.length === 0 ||
          current.number === currentSegment[currentSegment.length - 1].number + 1) {
        currentSegment.push(current);
      } else {
        // 编号不连续，检查当前段是否有效（至少3章）
        if (currentSegment.length >= 3) {
          continuousSegments.push([...currentSegment]);
        }
        // 开始新的段
        currentSegment = [current];
      }
    }

    // 检查最后一个段（至少3章）
    if (currentSegment.length >= 3) {
      continuousSegments.push(currentSegment);
    }

    // 严格的前后连续性验证 - 要求至少有两章连续
    const validChapters: typeof chaptersWithNumbers = [];

    for (const segment of continuousSegments) {
      for (let i = 0; i < segment.length; i++) {
        const current = segment[i];
        const prev = segment[i - 1];
        const next = segment[i + 1];

        // 检查前后连续性
        const hasPrevContinuous = prev && (prev.number === current.number - 1);
        const hasNextContinuous = next && (next.number === current.number + 1);

        // 更严格的验证：章节必须至少有前面或后面的两章连续
        const hasAtLeastTwoContinuous =
          // 情况1：当前章节有前一章和后一章
          (hasPrevContinuous && hasNextContinuous) ||
          // 情况2：当前章节有前一章，且前一章也有前一章（至少三章连续）
          (hasPrevContinuous && i >= 2 && segment[i-2] && segment[i-2].number === current.number - 2) ||
          // 情况3：当前章节有后一章，且后一章也有后一章（至少三章连续）
          (hasNextContinuous && i <= segment.length - 3 && segment[i+2] && segment[i+2].number === current.number + 2) ||
          // 情况4：段的开头，但必须至少有后续两章
          (i === 0 && hasNextContinuous && i <= segment.length - 3 && segment[i+2] && segment[i+2].number === current.number + 2) ||
          // 情况5：段的结尾，但必须至少有前面两章
          (i === segment.length - 1 && hasPrevContinuous && i >= 2 && segment[i-2] && segment[i-2].number === current.number - 2);

        if (hasAtLeastTwoContinuous) {
          validChapters.push(current);
        }
      }
    }

    console.log(`连续性检测：从${matches.length}个候选章节中识别出${continuousSegments.length}个连续段，验证后保留${validChapters.length}个有效章节`);

    // 如果没有找到有效章节，返回原始结果
    if (validChapters.length === 0) {
      console.log('严格连续性检测未找到有效章节，使用原始章节识别结果');
      return matches;
    }

    // 按原始位置排序返回
    return validChapters.sort((a, b) => a.index - b.index);
  };

  // 解析章节函数
  const parseChapters = (text: string): Chapter[] => {
    // 过滤掉常见的电子书水印和版权声明
    const cleanText = text.replace(/☆[^☆]*?看帮网[^☆]*?☆/g, '').replace(/☆本文由.*?所有☆/g, '').replace(/☆请勿用于商业.*?自负☆/g, '').replace(/☆https?:\/\/www\.kanbang\.cc☆/g, '');

    // 章节识别的正则表达式
    // 1. 章节格式
    const chapterRegex = /(第[零一二两三四五六七八九十百千0-9]+章(\s+[^\n]+)?|第[0-9]{1,4}章(\s+[^\n]+)?|Chapter\s+[0-9]+(\s+[^\n]+)?|CHAPTER\s+[0-9]+(\s+[^\n]+)?)/gi;

    // 2. 卷/册/部等结构，这些不直接作为分章依据，但是可以记录
    const volumeRegex = /(第[零一二两三四五六七八九十百千0-9]+[卷册部篇集](\s+[^\n]+)?)/gi;

    // 查找所有章节标记及其位置
    let chapterMatches: { title: string, index: number }[] = [];
    let match;

    // 查找正式章节
    while ((match = chapterRegex.exec(cleanText)) !== null) {
      // 防止重复匹配相同位置的章节
      let isDuplicate = false;
      for (const existingMatch of chapterMatches) {
        // 检查是否有重叠：如果新匹配的起始位置在某个现有匹配的范围内
        if (Math.abs(existingMatch.index - match.index) < 10) {
          isDuplicate = true;
          break;
        }
      }

      if (!isDuplicate) {
        console.log(`找到章节: "${match[0]}" 在位置: ${match.index}`);
        chapterMatches.push({
          title: match[0].trim(),
          index: match.index
        });
      }
    }

    // 如果没有找到任何章节标题
    if (chapterMatches.length === 0) {
      console.log("未找到标准章节格式，尝试通过卷/册/部来分章");

      // 尝试使用卷/册/部等结构分章
      while ((match = volumeRegex.exec(cleanText)) !== null) {
        // 防止重复匹配
        let isDuplicate = false;
        for (const existingMatch of chapterMatches) {
          if (Math.abs(existingMatch.index - match.index) < 10) {
            isDuplicate = true;
            break;
          }
        }

        if (!isDuplicate) {
          console.log(`找到卷/册/部: "${match[0]}" 在位置: ${match.index}`);
          chapterMatches.push({
            title: match[0].trim(),
            index: match.index
          });
        }
      }
    }

    // 按位置排序章节标题
    chapterMatches.sort((a, b) => a.index - b.index);

    // 如果仍然没有找到任何章节或卷结构
    if (chapterMatches.length === 0) {
      return [];
    }

    // 连续性检测：过滤掉不连续的章节
    const filteredMatches = filterChaptersByContinuity(chapterMatches);

    // 提取章节内容
    const chapters: Chapter[] = [];

    for (let i = 0; i < filteredMatches.length; i++) {
      const current = filteredMatches[i];
      const next = filteredMatches[i + 1];

      // 计算章节标题的结束位置（标题之后的内容）
      const titleEndIndex = current.index + current.title.length;

      const chapterContent = next
        ? cleanText.substring(titleEndIndex, next.index).trim()
        : cleanText.substring(titleEndIndex).trim();

      chapters.push({
        order: i + 1,
        title: current.title,
        content: chapterContent
      });
    }

    return chapters;
  };

  // 切换到文件上传模式
  const toggleFileUploadMode = () => {
    setFileUploadMode(!fileUploadMode);
    if (fileUploadMode) {
      // 切换回普通模式时清理文件相关状态
      setUploadedFile(null);
      setChapterPreview([]);
      setShowPreview(false);
    }
  };



  // 过滤作品
  const filteredWorks = works.filter((work) => {
    if (filter === 'all') return true;
    return work.type === filter;
  });

  // 排序作品
  const sortedWorks = filteredWorks.sort((a, b) => {
    if (sortBy === 'updated') return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    if (sortBy === 'created') return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    if (sortBy === 'title') return a.title.localeCompare(b.title);
    return 0;
  });

  // 搜索作品
  const searchedWorks = sortedWorks.filter((work) =>
    work.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 作品卡片组件
  const WorkCard = ({ work, onDelete }: { work: Work; onDelete: (id: number) => Promise<void> }) => {
    // 根据作品类型获取颜色类
    const getColorClass = (type: string) => {
      switch (type) {
        case 'novel':
          return {
            text: 'text-[#7D85CC]',
            border: 'border-[#7D85CC]',
            hover: 'hover:bg-[rgba(125,133,204,0.1)]',
            badge: 'badge-blue',
            bg: 'bg-[rgba(125,133,204,0.15)]'
          };
        case 'character':
          return {
            text: 'text-[#C281D3]',
            border: 'border-[#C281D3]',
            hover: 'hover:bg-[rgba(194,129,211,0.1)]',
            badge: 'badge-purple',
            bg: 'bg-[rgba(194,129,211,0.15)]'
          };
        case 'worldbuilding':
          return {
            text: 'text-[#78B48C]',
            border: 'border-[#78B48C]',
            hover: 'hover:bg-[rgba(120,180,140,0.1)]',
            badge: 'badge-green',
            bg: 'bg-[rgba(120,180,140,0.15)]'
          };
        case 'plot':
          return {
            text: 'text-[#E0976F]',
            border: 'border-[#E0976F]',
            hover: 'hover:bg-[rgba(224,149,117,0.1)]',
            badge: 'badge-yellow',
            bg: 'bg-[rgba(224,149,117,0.15)]'
          };
        case 'short_story':
          return {
            text: 'text-[#E06F51]',
            border: 'border-[#E06F51]',
            hover: 'hover:bg-[rgba(224,111,81,0.1)]',
            badge: 'badge-orange',
            bg: 'bg-[rgba(224,111,81,0.15)]'
          };
        case 'script':
          return {
            text: 'text-[#6F9CE0]',
            border: 'border-[#6F9CE0]',
            hover: 'hover:bg-[rgba(111,156,224,0.1)]',
            badge: 'badge-indigo',
            bg: 'bg-[rgba(111,156,224,0.15)]'
          };
        default:
          return {
            text: 'text-[#7D85CC]',
            border: 'border-[#7D85CC]',
            hover: 'hover:bg-[rgba(125,133,204,0.1)]',
            badge: 'badge-blue',
            bg: 'bg-[rgba(125,133,204,0.15)]'
          };
      }
    };

    const colors = getColorClass(work.type);

    const getTypeName = (type: string) => {
      switch (type) {
        case 'novel': return '小说';
        case 'character': return '角色';
        case 'worldbuilding': return '世界';
        case 'plot': return '情节';
        case 'short_story': return '短篇';
        case 'script': return '剧本';
        default: return '作品';
      }
    };

    return (
      <div
        className="ghibli-card h-80 text-center cursor-pointer relative"
        onClick={() => router.push(`/works/${work.id}`)}
      >
        <div className="tape" style={{ backgroundColor: `rgba(${colors.text.substring(6, colors.text.length - 1)}, 0.7)` }}>
          <div className="tape-texture"></div>
        </div>
        <div className="flex flex-col items-center h-full relative pb-12 pt-8">
          <span className={`material-icons ${colors.text} mb-6`} style={{ fontSize: '56px' }}>auto_stories</span>
          <h3 className="font-medium text-text-dark text-xl mb-2 text-center" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>{work.title}</h3>
          <p className="text-text-medium text-sm px-4 line-clamp-2 text-center">{work.description || "暂无描述"}</p>
        </div>

        {/* 左下角分类和时间 - 与设置按钮同层级，确保平行 */}
        <div className="absolute bottom-2 left-2 flex items-center space-x-2 z-10">
          <span className={`text-xs px-2 py-0.5 rounded-full ${colors.text} bg-white border ${colors.border}`}>
            {getTypeName(work.type)}
          </span>
          <span className="text-text-medium text-xs flex items-center">
            <span className="material-icons text-xs mr-1">calendar_today</span>
            {new Date(work.updatedAt).toLocaleDateString()}
          </span>
        </div>

        {/* 右下角设置按钮 - 放在卡片外层，避免被翘边影响 */}
        <div className="absolute bottom-2 right-2 z-10">
          <DropdownMenu
            trigger={
              <button className="w-10 h-10 rounded-full hover:bg-[rgba(120,180,140,0.15)] bg-white/90 backdrop-blur-sm transition-all duration-200 text-text-light hover:text-text-medium shadow-md hover:shadow-lg border border-[rgba(120,180,140,0.2)] flex items-center justify-center">
                <span className="material-icons text-base">settings</span>
              </button>
            }
            items={[
              {
                id: 'edit',
                label: '编辑',
                icon: 'edit',
                onClick: () => handleEditWork(work),
                className: `${colors.text} hover:bg-[rgba(120,180,140,0.1)]`
              },
              {
                id: 'export',
                label: '导出TXT',
                icon: 'download',
                onClick: () => handleExportTxt(work),
                className: 'text-[#78B48C] hover:bg-[rgba(120,180,140,0.1)]'
              },
              {
                id: 'delete',
                label: '删除',
                icon: 'delete',
                onClick: () => onDelete(work.id!),
                className: 'text-[#E06F6F] hover:bg-[rgba(224,111,111,0.1)]',
                disabled: isDeleting
              }
            ]}
            align="right"
          />
        </div>
        <div className="page-curl"></div>
      </div>
    );
  };









  return (
    <div className="flex h-screen bg-bg-color animate-fadeIn overflow-hidden">
      {/* 主内容区 */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* 使用通用顶边栏组件 */}
        <TopBar
          showBackButton={true}
          actions={
            <>
              <button
                className="hidden md:flex ghibli-button text-sm"
                onClick={handleCreateWork}
              >
                <span className="material-icons mr-1 text-sm">add</span>
                创建新作品
              </button>

              <button
                className="md:hidden round-button"
                onClick={handleCreateWork}
                aria-label="创建新作品"
              >
                <span className="material-icons">add</span>
              </button>

            </>
          }
        />

        {/* 主要内容 */}
        <div className="flex-1 p-3 md:p-6 lg:p-8 overflow-auto">
          <div className="max-w-6xl mx-auto">
            {/* 过滤和排序 */}
            <div className="flex flex-wrap items-center justify-between mb-4 md:mb-6 gap-2">
              <div className="flex flex-wrap items-center gap-2">
                <div className="relative">
                  <select
                    className="pl-9 pr-4 py-1.5 text-xs md:text-sm text-text-medium bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-green appearance-none"
                    value={filter}
                    onChange={(e) => setFilter(e.target.value)}
                  >
                    <option value="all">全部作品</option>
                    <option value="novel">小说</option>
                    <option value="short_story">短篇</option>
                    <option value="script">剧本</option>
                  </select>
                  <span className="material-icons absolute left-2 top-1/2 -translate-y-1/2 text-text-light text-lg">filter_list</span>
                </div>
                <div className="relative">
                  <select
                    className="pl-9 pr-4 py-1.5 text-xs md:text-sm text-text-medium bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-green appearance-none"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                  >
                    <option value="updated">最近更新</option>
                    <option value="created">创建时间</option>
                    <option value="title">标题</option>
                  </select>
                  <span className="material-icons absolute left-2 top-1/2 -translate-y-1/2 text-text-light text-lg">sort</span>
                </div>
              </div>
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索作品..."
                  className="pl-9 pr-4 py-1.5 text-xs md:text-sm w-full md:w-64 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-green"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <span className="material-icons absolute left-2 top-1/2 -translate-y-1/2 text-text-light text-lg">search</span>
                {searchQuery && (
                  <button
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-text-light hover:text-text-medium"
                    onClick={() => setSearchQuery('')}
                  >
                    <span className="material-icons text-lg">close</span>
                  </button>
                )}
              </div>
            </div>

            {/* 作品卡片网格 */}
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-green"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {/* 创建新作品的专属卡片 */}
                <div
                  className="ghibli-card h-80 text-center cursor-pointer bg-gradient-to-br from-[rgba(120,180,140,0.05)] to-[rgba(125,133,204,0.1)] hover:from-[rgba(120,180,140,0.1)] hover:to-[rgba(125,133,204,0.15)] transition-all duration-300 hover:shadow-xl hover:-translate-y-1"
                  onClick={() => handleCreateWork()}
                >
                  <div className="tape bg-gradient-to-r from-[rgba(120,180,140,0.7)] to-[rgba(125,133,204,0.7)]">
                    <div className="tape-texture"></div>
                  </div>
                  <div className="flex flex-col items-center justify-center h-full">
                    <div className="w-16 h-16 bg-gradient-to-r from-[rgba(120,180,140,0.2)] to-[rgba(125,133,204,0.2)] rounded-full flex items-center justify-center mb-4 shadow-inner group-hover:scale-110 transition-all duration-300 hover:scale-110">
                      <span className="material-icons text-[#78B48C] text-3xl transform transition-transform duration-300 hover:rotate-12">edit</span>
                    </div>
                    <h3 className="font-medium text-text-dark text-xl mb-3" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>创建新作品</h3>
                    <p className="text-text-medium text-sm mb-6 px-6">开始你的创作之旅，记录灵感与故事</p>
                    <div className="flex justify-center space-x-3">
                      <button
                        className="px-3 py-1.5 rounded-full text-white bg-[#78B48C] hover:bg-[#6AA47C] transition-all duration-200 text-xs flex items-center shadow-sm hover:shadow hover:scale-105"
                        onClick={(e) => {
                          e.stopPropagation();
                          setFileUploadMode(false);
                          handleCreateWork();
                        }}
                      >
                        <span className="material-icons text-xs mr-1">edit</span>
                        新建
                      </button>
                      <button
                        className="px-3 py-1.5 rounded-full text-white bg-[#9C6FE0] hover:bg-[#8D60D1] transition-all duration-200 text-xs flex items-center shadow-sm hover:shadow hover:scale-105"
                        onClick={(e) => {
                          e.stopPropagation();
                          setFileUploadMode(true);
                          handleCreateWork();
                        }}
                      >
                        <span className="material-icons text-xs mr-1">upload_file</span>
                        导入TXT
                      </button>
                    </div>
                  </div>
                  <div className="page-curl"></div>
                </div>

                {/* 现有作品列表 */}
                {searchedWorks.map((work) => (
                  <WorkCard key={work.id} work={work} onDelete={handleDeleteWork} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创建/编辑作品弹窗 */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-card-color rounded-2xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-auto border border-[rgba(120,180,140,0.4)]">
            <div className="sticky top-0 bg-card-color p-6 border-b border-[rgba(120,180,140,0.3)] flex justify-between items-center rounded-t-2xl z-10">
              <h2 className="text-xl font-ma-shan text-text-dark">{isEditMode ? '编辑作品' : '创建新作品'}</h2>
              <button
                className="p-2 hover:bg-[rgba(120,180,140,0.1)] rounded-full transition-colors duration-200 text-text-medium"
                onClick={handleCloseModal}
              >
                <span className="material-icons">close</span>
              </button>
            </div>

            <div className="p-6">
              {!isEditMode && (
                <div className="mb-6 flex border-b border-[rgba(120,180,140,0.2)]">
                  <button
                    className={`px-4 py-2 font-medium text-sm ${!fileUploadMode ? 'text-[#7D85CC] border-b-2 border-[#7D85CC]' : 'text-text-medium hover:text-text-dark'}`}
                    onClick={() => setFileUploadMode(false)}
                  >
                    <span className="material-icons text-sm mr-1 align-text-bottom">edit</span>
                    手动创建
                  </button>
                  <button
                    className={`px-4 py-2 font-medium text-sm ${fileUploadMode ? 'text-[#7D85CC] border-b-2 border-[#7D85CC]' : 'text-text-medium hover:text-text-dark'}`}
                    onClick={() => setFileUploadMode(true)}
                  >
                    <span className="material-icons text-sm mr-1 align-text-bottom">upload_file</span>
                    导入TXT
                  </button>
                </div>
              )}

              {error && (
                <div className="mb-6 p-4 bg-[rgba(224,111,111,0.1)] border border-[rgba(224,111,111,0.3)] rounded-xl text-[#E06F6F]">
                  <div className="flex items-center">
                    <span className="material-icons mr-2 text-[#E06F6F]">error</span>
                    <span>{error}</span>
                  </div>
                </div>
              )}

              {fileUploadMode && !isEditMode ? (
                <div>
                  {!showPreview ? (
                    <div className="border-2 border-dashed border-[rgba(120,180,140,0.4)] rounded-xl p-6 flex flex-col items-center justify-center mb-6 bg-[rgba(120,180,140,0.05)]">
                      <span className="material-icons text-4xl text-[#9C6FE0] mb-2">upload_file</span>
                      <p className="text-text-medium mb-4">选择或拖放TXT文件</p>
                      <input
                        type="file"
                        accept=".txt"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="file-upload"
                        disabled={uploading}
                      />
                      <label
                        htmlFor="file-upload"
                        className="px-5 py-2 rounded-full bg-[#9C6FE0] text-white hover:bg-[#8D60D1] transition-colors duration-200 flex items-center cursor-pointer"
                      >
                        {uploading ? (
                          <>
                            <span className="material-icons animate-spin mr-2 text-sm">refresh</span>
                            <span>处理中...</span>
                          </>
                        ) : (
                          <>
                            <span className="material-icons mr-2 text-sm">file_upload</span>
                            <span>选择文件</span>
                          </>
                        )}
                      </label>
                      <p className="text-text-light text-xs mt-4">支持TXT格式，自动识别章节结构，最大支持20MB文件</p>
                    </div>
                  ) : (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="font-medium text-text-dark font-ma-shan">已识别 {chapterPreview.length} 个章节</h3>
                          {uploadedFile && (
                            <p className="text-text-light text-xs mt-1">
                              文件：{uploadedFile.name} ({(uploadedFile.size / (1024 * 1024)).toFixed(2)}MB)
                            </p>
                          )}
                        </div>
                        <button
                          className="text-[#7D85CC] text-sm flex items-center hover:text-[#6970B9] transition-colors duration-200"
                          onClick={() => setShowPreview(false)}
                        >
                          <span className="material-icons text-sm mr-1">arrow_back</span>
                          重新选择
                        </button>
                      </div>

                      <div className="border border-[rgba(120,180,140,0.3)] rounded-xl overflow-hidden mb-4 bg-card-color">
                        <div className="bg-[rgba(120,180,140,0.1)] px-4 py-2 border-b border-[rgba(120,180,140,0.2)]">
                          <p className="text-sm text-text-medium">章节预览</p>
                        </div>
                        <div className="max-h-64 overflow-auto p-2">
                          {chapterPreview.map((chapter, index) => (
                            <div key={index} className="border-b border-[rgba(120,180,140,0.1)] last:border-b-0 py-2">
                              <p className="font-medium text-text-dark">{chapter.title}</p>
                              <p className="text-text-medium text-sm truncate">{chapter.content.substring(chapter.title.length, chapter.title.length + 100)}...</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="mb-6">
                        <label htmlFor="title" className="block text-text-dark font-medium mb-2 font-ma-shan">作品标题</label>
                        <input
                          type="text"
                          id="title"
                          name="title"
                          value={formData.title}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-[rgba(120,180,140,0.4)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[#7D85CC] focus:border-transparent bg-card-color text-text-dark"
                          placeholder="请输入作品标题"
                          required
                        />
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="mb-6">
                    <label htmlFor="title" className="block text-text-dark font-medium mb-2 font-ma-shan">作品标题</label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-[rgba(120,180,140,0.4)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[#7D85CC] focus:border-transparent bg-card-color text-text-dark"
                      placeholder="请输入作品标题"
                      required
                    />
                  </div>

                  <div className="flex justify-end space-x-4 sticky bottom-0 pt-4 bg-card-color border-t border-[rgba(120,180,140,0.2)] mt-6">
                    <button
                      type="submit"
                      className="px-5 py-2 rounded-full bg-[#7D85CC] text-white hover:bg-[#6970B9] transition-colors duration-200 flex items-center"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <span className="material-icons animate-spin mr-2 text-sm">refresh</span>
                          <span>{isEditMode ? '保存中...' : '创建中...'}</span>
                        </>
                      ) : (
                        <>
                          <span className="material-icons mr-2 text-sm">save</span>
                          <span>{isEditMode ? '保存修改' : '创建作品'}</span>
                        </>
                      )}
                    </button>
                    <button
                      type="button"
                      className="px-5 py-2 rounded-full border border-[#7D85CC] text-[#7D85CC] hover:bg-[rgba(125,133,204,0.1)] transition-colors duration-200"
                      onClick={handleCloseModal}
                    >
                      取消
                    </button>
                  </div>
                </form>
              )}

              {fileUploadMode && showPreview && (
                <div className="flex justify-end space-x-4 sticky bottom-0 pt-4 bg-card-color border-t border-[rgba(120,180,140,0.2)] mt-6">
                  <button
                    type="button"
                    className="px-5 py-2 rounded-full bg-[#9C6FE0] text-white hover:bg-[#8D60D1] transition-colors duration-200 flex items-center"
                    onClick={handleSubmit}
                    disabled={isLoading || chapterPreview.length === 0}
                  >
                    {isLoading ? (
                      <>
                        <span className="material-icons animate-spin mr-2 text-sm">refresh</span>
                        <span>导入中...</span>
                      </>
                    ) : (
                      <>
                        <span className="material-icons mr-2 text-sm">file_download_done</span>
                        <span>导入作品</span>
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    className="px-5 py-2 rounded-full border border-[#7D85CC] text-[#7D85CC] hover:bg-[rgba(125,133,204,0.1)] transition-colors duration-200"
                    onClick={handleCloseModal}
                  >
                    取消
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 删除确认弹窗 */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-card-color rounded-2xl shadow-xl w-[420px] overflow-hidden border border-[rgba(224,111,81,0.4)]">
            <div className="sticky top-0 bg-[rgba(224,111,81,0.1)] p-6 border-b border-[rgba(224,111,81,0.3)] flex justify-between items-center rounded-t-2xl z-10">
              <h2 className="text-xl font-ma-shan text-[#E06F51]">确认删除作品</h2>
              <button
                className="p-2 hover:bg-[rgba(224,111,81,0.1)] rounded-full transition-colors duration-200 text-text-medium"
                onClick={cancelDelete}
              >
                <span className="material-icons">close</span>
              </button>
            </div>

            <div className="p-6">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-full bg-[rgba(224,111,81,0.1)] flex items-center justify-center mr-4">
                  <span className="material-icons text-[#E06F51] text-2xl">warning</span>
                </div>
                <div>
                  <p className="text-text-dark font-medium mb-1">此操作不可恢复</p>
                  <p className="text-text-medium text-sm">确定要删除这个作品吗？所有相关的章节内容将永久丢失。</p>
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4 border-t border-[rgba(224,111,81,0.2)]">
                <button
                  type="button"
                  className="px-5 py-2 rounded-full bg-[#E06F51] text-white hover:bg-[#D05E40] transition-colors duration-200 flex items-center"
                  onClick={confirmDelete}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <span className="material-icons animate-spin mr-2 text-sm">refresh</span>
                      <span>删除中...</span>
                    </>
                  ) : (
                    <>
                      <span className="material-icons mr-2 text-sm">delete</span>
                      <span>确认删除</span>
                    </>
                  )}
                </button>
                <button
                  type="button"
                  className="px-5 py-2 rounded-full border border-[#7D85CC] text-[#7D85CC] hover:bg-[rgba(125,133,204,0.1)] transition-colors duration-200"
                  onClick={cancelDelete}
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}