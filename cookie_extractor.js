/**
 * Cookie提取工具
 * 从浏览器中提取认证Cookie用于测试脚本
 */

const http = require('http');

// 测试不同的Cookie组合
async function testCookieAuth(cookies) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/prompt?id=056c4636-14d7-4eb1-abd0-db58ad305728',
      method: 'GET',
      headers: {
        'Cookie': cookies,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        let parsedResponse;
        try {
          parsedResponse = JSON.parse(data);
        } catch (e) {
          parsedResponse = data;
        }
        
        resolve({
          statusCode: res.statusCode,
          success: res.statusCode === 200,
          response: parsedResponse,
          rawResponse: data
        });
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.end();
  });
}

// 常见的Supabase Cookie模式
const commonSupabaseCookies = [
  // Next.js + Supabase常见的Cookie名称
  'sb-localhost-auth-token',
  'sb-localhost-auth-token.0',
  'sb-localhost-auth-token.1',
  'supabase-auth-token',
  'supabase.auth.token',
  '__Secure-next-auth.session-token',
  'next-auth.session-token',
  // 其他可能的认证Cookie
  'auth-token',
  'session',
  'access_token',
  'refresh_token'
];

// 生成测试Cookie字符串
function generateTestCookies() {
  const testCookies = [];
  
  // 基于JWT Token生成可能的Cookie值
  const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.o7BIyZ_OrLVFhKBMnLG928OF9lJHcWsNJd56lA1YeAY';
  
  // 尝试不同的Cookie名称和格式
  commonSupabaseCookies.forEach(cookieName => {
    // 直接使用JWT Token
    testCookies.push(`${cookieName}=${jwtToken}`);
    
    // 使用base64编码的JWT Token
    const base64Token = Buffer.from(jwtToken).toString('base64');
    testCookies.push(`${cookieName}=${base64Token}`);
    
    // 使用JSON格式包装的Token
    const jsonToken = JSON.stringify({
      access_token: jwtToken,
      token_type: 'bearer',
      expires_in: 3600,
      refresh_token: 'refresh_token_placeholder'
    });
    const encodedJsonToken = encodeURIComponent(jsonToken);
    testCookies.push(`${cookieName}=${encodedJsonToken}`);
  });
  
  return testCookies;
}

// 主测试函数
async function findWorkingCookie() {
  console.log('🔍 开始查找有效的Cookie认证方式');
  console.log('='.repeat(50));
  
  const testCookies = generateTestCookies();
  console.log(`📋 生成了 ${testCookies.length} 种Cookie组合进行测试`);
  
  let workingCookie = null;
  let testCount = 0;
  
  for (const cookie of testCookies) {
    testCount++;
    const cookieName = cookie.split('=')[0];
    
    console.log(`\n🧪 测试 ${testCount}/${testCookies.length}: ${cookieName}`);
    
    const result = await testCookieAuth(cookie);
    
    if (result.success) {
      console.log('✅ 成功！找到有效的Cookie');
      console.log(`   Cookie名称: ${cookieName}`);
      console.log(`   响应状态: ${result.statusCode}`);
      workingCookie = cookie;
      break;
    } else {
      console.log(`❌ 失败: ${result.error || `HTTP ${result.statusCode}`}`);
      if (result.response && typeof result.response === 'object' && result.response.error) {
        console.log(`   错误详情: ${result.response.error}`);
      }
    }
    
    // 添加小延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (workingCookie) {
    console.log('🎉 找到有效的Cookie认证方式！');
    console.log(`Cookie: ${workingCookie}`);
    
    // 测试并发请求
    console.log('\n🚀 测试并发请求...');
    const concurrentResults = await testConcurrentWithCookie(workingCookie);
    
    return {
      success: true,
      cookie: workingCookie,
      concurrentTest: concurrentResults
    };
  } else {
    console.log('❌ 未找到有效的Cookie认证方式');
    console.log('\n💡 建议:');
    console.log('   1. 检查浏览器是否已登录');
    console.log('   2. 手动从浏览器开发者工具复制Cookie');
    console.log('   3. 检查服务端认证逻辑');
    
    return {
      success: false,
      cookie: null
    };
  }
}

// 使用找到的Cookie进行并发测试
async function testConcurrentWithCookie(cookie, concurrency = 5) {
  console.log(`🔄 开始并发测试 (${concurrency}个请求)...`);
  
  const promises = Array.from({ length: concurrency }, (_, i) => 
    testCookieAuth(cookie).then(result => ({ requestId: i + 1, ...result }))
  );
  
  const results = await Promise.all(promises);
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ 成功: ${successful}/${concurrency}`);
  console.log(`❌ 失败: ${failed}/${concurrency}`);
  
  if (failed > 0) {
    console.log('\n失败的请求详情:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  请求${r.requestId}: ${r.error || `HTTP ${r.statusCode}`}`);
    });
  }
  
  return { successful, failed, results };
}

// 运行测试
if (require.main === module) {
  findWorkingCookie().then(result => {
    if (result.success) {
      console.log('\n🎯 测试完成！可以在压力测试脚本中使用以下Cookie:');
      console.log(`"${result.cookie}"`);
    }
  }).catch(console.error);
}

module.exports = {
  testCookieAuth,
  findWorkingCookie,
  testConcurrentWithCookie
};
