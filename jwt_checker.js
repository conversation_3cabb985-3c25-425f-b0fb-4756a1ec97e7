/**
 * JWT Token 检测和分析工具
 * 用于诊断认证问题和Token状态
 */

const http = require('http');

// JWT Token解析函数
function parseJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return { error: 'Invalid JWT format' };
    }
    
    const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    
    return { header, payload };
  } catch (error) {
    return { error: error.message };
  }
}

// 时间戳转换函数
function formatTimestamp(timestamp) {
  const date = new Date(timestamp * 1000);
  return {
    timestamp,
    date: date.toISOString(),
    localTime: date.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
    fromNow: Math.round((Date.now() - timestamp * 1000) / 1000)
  };
}

// JWT状态检查
function checkJWTStatus(token) {
  const parsed = parseJWT(token);
  if (parsed.error) {
    return { valid: false, error: parsed.error };
  }
  
  const now = Math.floor(Date.now() / 1000);
  const { payload } = parsed;
  
  const issued = formatTimestamp(payload.iat);
  const expires = formatTimestamp(payload.exp);
  const timeToExpiry = payload.exp - now;
  
  return {
    valid: true,
    isExpired: now > payload.exp,
    timeToExpiry,
    timeToExpiryMinutes: Math.round(timeToExpiry / 60),
    issued,
    expires,
    userInfo: {
      sub: payload.sub,
      email: payload.email,
      role: payload.role,
      sessionId: payload.session_id
    }
  };
}

// 测试API认证
function testAPIAuth(token) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/prompt?id=056c4636-14d7-4eb1-abd0-db58ad305728',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        let parsedResponse;
        try {
          parsedResponse = JSON.parse(data);
        } catch (e) {
          parsedResponse = data;
        }

        resolve({
          statusCode: res.statusCode,
          success: res.statusCode === 200,
          response: parsedResponse,
          rawResponse: data,
          headers: res.headers
        });
      });
    });
    
    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.end();
  });
}

// 并发认证测试
async function testConcurrentAuth(token, concurrency = 5) {
  console.log(`🔄 开始并发认证测试 (${concurrency}个请求)...`);
  
  const promises = Array.from({ length: concurrency }, (_, i) => 
    testAPIAuth(token).then(result => ({ requestId: i + 1, ...result }))
  );
  
  const results = await Promise.all(promises);
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ 成功: ${successful}/${concurrency}`);
  console.log(`❌ 失败: ${failed}/${concurrency}`);
  
  if (failed > 0) {
    console.log('\n失败的请求详情:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  请求${r.requestId}: ${r.error || `HTTP ${r.statusCode}`}`);
    });
  }
  
  return { successful, failed, results };
}

// 主检测函数
async function checkJWTHealth() {
  console.log('🔍 JWT Token 健康检查');
  console.log('='.repeat(50));
  
  // 当前使用的Token
  const currentToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.o7BIyZ_OrLVFhKBMnLG928OF9lJHcWsNJd56lA1YeAY';
  
  // 1. 解析Token
  console.log('📋 Token 基本信息:');
  const status = checkJWTStatus(currentToken);
  
  if (!status.valid) {
    console.log(`❌ Token无效: ${status.error}`);
    return;
  }
  
  console.log(`   用户: ${status.userInfo.email}`);
  console.log(`   UUID: ${status.userInfo.sub}`);
  console.log(`   会话ID: ${status.userInfo.sessionId}`);
  console.log(`   签发时间: ${status.issued.localTime} (${status.issued.fromNow}秒前)`);
  console.log(`   过期时间: ${status.expires.localTime} (${status.timeToExpiryMinutes}分钟后)`);
  console.log(`   状态: ${status.isExpired ? '❌ 已过期' : '✅ 有效'}`);
  
  if (status.isExpired) {
    console.log('\n⚠️  Token已过期，需要重新获取！');
    return;
  }
  
  if (status.timeToExpiryMinutes < 5) {
    console.log('\n⚠️  Token即将过期，建议重新获取！');
  }
  
  // 2. 单次API测试
  console.log('\n🔧 API认证测试:');
  const singleTest = await testAPIAuth(currentToken);
  console.log(`   单次请求: ${singleTest.success ? '✅ 成功' : '❌ 失败'}`);
  if (!singleTest.success) {
    console.log(`   错误: ${singleTest.error || `HTTP ${singleTest.statusCode}`}`);
    if (singleTest.response && typeof singleTest.response === 'object') {
      console.log(`   详情: ${JSON.stringify(singleTest.response, null, 2)}`);
    } else if (singleTest.rawResponse) {
      console.log(`   响应: ${singleTest.rawResponse.substring(0, 200)}...`);
    }
  }
  
  // 3. 并发测试
  console.log('\n🚀 并发认证测试:');
  const concurrentTest = await testConcurrentAuth(currentToken, 10);
  
  // 4. 问题诊断
  console.log('\n🔍 问题诊断:');
  
  if (concurrentTest.failed === 0) {
    console.log('✅ 所有测试通过，Token状态正常');
  } else if (concurrentTest.successful > 0 && concurrentTest.failed > 0) {
    console.log('⚠️  部分请求失败，可能存在以下问题:');
    console.log('   1. 服务端会话状态不一致');
    console.log('   2. 并发请求时的竞争条件');
    console.log('   3. Supabase客户端会话管理问题');
    console.log('   4. 网络延迟导致的时序问题');
  } else {
    console.log('❌ 所有请求失败，Token可能已失效');
  }
  
  // 5. 建议
  console.log('\n💡 建议:');
  if (status.timeToExpiryMinutes < 30) {
    console.log('   - 重新获取JWT Token');
  }
  if (concurrentTest.failed > 0) {
    console.log('   - 在压力测试前验证Token有效性');
    console.log('   - 考虑在测试脚本中添加Token刷新机制');
    console.log('   - 减少并发数量或增加请求间隔');
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('检查完成时间:', new Date().toLocaleString('zh-CN'));
}

// 实时监控模式
async function monitorJWT(intervalMinutes = 5) {
  console.log(`🔄 开始JWT实时监控 (每${intervalMinutes}分钟检查一次)`);
  console.log('按 Ctrl+C 停止监控\n');
  
  while (true) {
    await checkJWTHealth();
    console.log(`\n⏰ 等待${intervalMinutes}分钟后进行下次检查...\n`);
    await new Promise(resolve => setTimeout(resolve, intervalMinutes * 60 * 1000));
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

if (command === 'monitor') {
  const interval = parseInt(args[1]) || 5;
  monitorJWT(interval);
} else {
  checkJWTHealth();
}

module.exports = {
  parseJWT,
  checkJWTStatus,
  testAPIAuth,
  testConcurrentAuth,
  checkJWTHealth
};
